"use client";

import { useQuery } from "convex/react";
import { Minus, Plus, StarIcon } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { Breadcrumbs } from "@/components/dashboard/breadcrumbs";
import Loading from "@/components/storefront/loading";
import { ProductImages } from "@/components/storefront/product/product-images";
import { ProductsCarousel } from "@/components/storefront/product/products-carousel";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { api } from "@/convex/_generated/api";
import { useCart } from "@/hooks/use-cart";
import { useWishlist } from "@/hooks/use-wishlist";
import { convertToLocale } from "@/utils/money";

export default function ProductPage() {
  const { slug } = useParams<{ slug: string }>();
  const product = useQuery(api.products.getBySlug, { slug });
  const relatedProducts = useQuery(
    api.products.list,
    !product ? "skip" : { brandId: product.brandId }
  );
  const [quantity, setQuantity] = useState(1);
  const { addItem } = useCart();
  const { addToWishlist, isInWishlist } = useWishlist();

  if (!product) {
    return <Loading />;
  }

  const handleIncrement = () => {
    if (quantity < product.stock) {
      setQuantity(quantity + 1);
    }
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleAddToCart = () => {
    addItem(product, quantity);
    toast.success("Added to cart");
  };

  const handleAddToWishlist = () => {
    addToWishlist(product);
    toast.success("Added to wishlist");
  };

  return (
    <div className="space-y-8 py-8">
      {/* Breadcrumb */}
      <Breadcrumbs
        breadcrumbs={[
          {
            title: "Home",
            href: "/"
          },
          {
            title: product.name,
            href: `/products/${product.slug}`,
            current: true
          }
        ]}
      />

      <div className="gap-12 grid grid-cols-1 md:grid-cols-2">
        {/* Product Images */}
        <div className="aspect-square">
          <ProductImages images={product.images} />
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            {product.name}
          </Typography>
          {/* Price */}
          <div className="mb-6">
            {product?.sale?.active ? (
              <div className="flex items-center">
                <Typography variant="price" className="mr-2 text-primary">
                  {convertToLocale({ amount: product.sale.price })}
                </Typography>
                <Typography variant="mutedText" className="line-through">
                  {convertToLocale({ amount: product.price })}
                </Typography>
                <span className="bg-primary ml-2 px-2 py-1 rounded-full text-primary-foreground text-xs">
                  SALE
                </span>
                <span className="bg-black ml-2 px-2 py-1 rounded-full text-primary-foreground text-sm uppercase">
                  -{product.sale.discountPercentage}%
                </span>
              </div>
            ) : (
              <Typography variant="price" className="text-primary">
                {convertToLocale({ amount: product.price })}
              </Typography>
            )}
          </div>

          {/* Availability */}
          <div className="mb-6">
            <Typography variant="body">
              {product.stock > 0 ? (
                <span className="text-green-600">
                  ✓ {product.stock} items in stock
                </span>
              ) : (
                <span className="text-red-500">✗ Out of stock</span>
              )}
            </Typography>
          </div>

          {/* Quantity */}
          <div className="mb-6">
            <Typography variant="body" weight="medium" className="mb-2">
              Quantity
            </Typography>
            <div className="flex gap-4 lg:gap-8">
              <div className="flex items-center border rounded w-fit">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDecrement}
                  className="border-r rounded-none"
                  disabled={quantity <= 1}
                >
                  <Minus size={16} />
                </Button>
                <input
                  type="text"
                  value={quantity}
                  readOnly
                  className="rounded-none focus:outline-none w-9 text-center"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleIncrement}
                  className="border-l rounded-none"
                  disabled={quantity >= product.stock}
                >
                  <Plus size={16} />
                </Button>
              </div>

              <Button
                onClick={handleAddToCart}
                className="flex-1 uppercase"
                disabled={product.stock === 0}
              >
                ADD TO CART
              </Button>
            </div>
          </div>

          {/* Action buttons */}
          <div className="space-y-4 mb-4">
            <Button
              className="bg-black w-full uppercase"
              onClick={handleAddToWishlist}
              disabled={isInWishlist(product._id)}
            >
              {isInWishlist(product._id)
                ? "ALREADY IN WISHLIST"
                : "ADD TO WISHLIST"}
            </Button>
          </div>

          <Accordion
            type="single"
            defaultValue="product-description"
            collapsible
          >
            <AccordionItem value="product-description">
              <AccordionTrigger icon={<StarIcon className="size-4" />}>
                <Typography
                  variant="body"
                  weight="medium"
                  className="font-playfair"
                >
                  Product Description
                </Typography>
              </AccordionTrigger>
              <AccordionContent>
                <Typography variant="p">
                  {product.description ||
                    "No description available for this product."}
                </Typography>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>

      {/* Similar Products */}
      <div className="mt-12">
        <Typography
          variant="h2"
          weight="semibold"
          className="mb-8 font-playfair"
        >
          You May Also Like{" "}
        </Typography>

        {relatedProducts && (
          <ProductsCarousel products={relatedProducts.products} />
        )}
      </div>
    </div>
  );
}

"use client";

import type { ColumnDef } from "@tanstack/react-table";

import Link from "next/link";

import type { Product } from "@/types";

import { Badge } from "@/components/ui/badge";
import { LOW_STOCK_THRESHOLD } from "@/config/constants";
import { formatDate } from "@/utils";

import { ProductActions } from "./product-actions";

export function columns(
  handleDelete: (id: string) => Promise<void>
): ColumnDef<Product>[] {
  return [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <div className="font-medium">
          <Link href={`/dashboard/products/${row.original._id}`}>
            {row.original.name}
          </Link>
        </div>
      ),
      sortingFn: "alphanumeric"
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => (
        <div className="max-w-[500px] truncate">
          {row.original.category.name}
        </div>
      ),
      sortingFn: "alphanumeric"
    },
    {
      accessorKey: "brand",
      header: "Brand",
      cell: ({ row }) => (
        <div className="max-w-[500px] truncate">{row.original.brand.name}</div>
      ),
      sortingFn: "alphanumeric"
    },
    {
      accessorKey: "scent",
      header: "Scent",
      cell: ({ row }) => {
        if (!row.original.scent) {
          return <span className="text-muted-foreground">None</span>;
        }
        return (
          <div className="max-w-[500px] truncate">
            {row.original.scent.name}
          </div>
        );
      },
      sortingFn: "alphanumeric"
    },
    {
      accessorKey: "price",
      header: "Price",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">${row.original.price.toFixed(2)}</div>
          {row.original.sale?.active && (
            <div className="text-xs text-red-500 ">
              {row.original.sale.discountPercentage}% off
            </div>
          )}
        </div>
      ),
      sortingFn: "basic"
    },
    {
      accessorKey: "stock",
      header: "Stock",
      cell: ({ row }) => (
        <div
          className={
            row.original.stock < LOW_STOCK_THRESHOLD
              ? "text-red-500 font-medium"
              : ""
          }
        >
          {row.original.stock}
        </div>
      ),
      sortingFn: "basic"
    },
    {
      accessorKey: "isPublished",
      header: "Status",
      cell: ({ row }) => (
        <div className="flex gap-1">
          <Badge
            variant="outline"
            className={
              row.original.isPublished
                ? "bg-green-50 text-green-700 hover:bg-green-50"
                : "bg-amber-50 text-amber-700 hover:bg-amber-50"
            }
          >
            {row.original.isPublished ? "Published" : "Draft"}
          </Badge>
          {row.original.isNew && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              New
            </Badge>
          )}
        </div>
      ),
      sortingFn: "basic"
    },
    {
      accessorKey: "_creationTime",
      header: "Created Date",
      cell: ({ row }) => (
        <div>{formatDate(new Date(row.original._creationTime))}</div>
      ),
      sortingFn: "datetime"
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <ProductActions product={row.original} onDelete={handleDelete} />
      ),
      enableSorting: false
    }
  ];
}

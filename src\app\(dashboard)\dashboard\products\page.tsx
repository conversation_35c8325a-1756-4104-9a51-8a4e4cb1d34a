"use client";

import { useMutation, useQuery } from "convex/react";
import { FilterX, Plus } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { DataTable } from "@/components/dashboard/data-table";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LOW_STOCK_THRESHOLD } from "@/config/constants";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { useStableQuery } from "@/hooks/use-stablequery";
import { tryCatch } from "@/utils/try-catch";

import { columns } from "./_components/columns";

type Filters = {
  categoryId?: Id<"categories"> | undefined;
  brandId?: Id<"brands"> | undefined;
  scentId?: Id<"scents"> | undefined;
  featured?: boolean | undefined;
};

const defaultFilters = {
  categoryId: undefined,
  brandId: undefined,
  scentId: undefined,
  featured: undefined
};

export default function ProductsPage() {
  const [filters, setFilters] = useState<Filters>({
    ...defaultFilters
  });
  const [viewMode, setViewMode] = useState("all");
  const productResults = useStableQuery(api.products.list, {
    isPublished: undefined,
    ...filters
  });
  const categories = useQuery(api.categories.list, { isActive: undefined });
  const brands = useQuery(api.brands.list, { isActive: undefined });
  const scents = useQuery(api.scents.list, { isActive: undefined });
  const deleteProduct = useMutation(api.products.remove);
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  const filteredProducts = [
    ...(productResults?.products
      ? viewMode === "low-stock"
        ? productResults?.products?.filter(
            (product) => product.stock <= LOW_STOCK_THRESHOLD
          )
        : productResults?.products
      : [])
  ];

  useEffect(() => {
    if (viewMode === "featured") {
      setFilters((prev) => ({ ...prev, featured: true }));
    } else {
      setFilters((prev) => ({ ...prev, featured: undefined }));
    }
  }, [viewMode]);

  if (!productResults || !categories || !brands || !scents) {
    return <Loading />;
  }

  const handleDeleteProduct = async (id: string) => {
    const { success, error } = await tryCatch(
      deleteProduct({ id: id as Id<"products"> })
    );

    if (success) {
      toast.success("Product deleted");
    } else {
      toast.error("Failed to delete product", {
        description: error.message
      });
    }
  };

  const resetFilters = () => {
    setFilters({
      categoryId: undefined,
      brandId: undefined,
      scentId: undefined,
      featured: undefined
    });
    setViewMode("all");
  };

  const hasActiveFilters =
    filters.categoryId ||
    filters.brandId ||
    filters.scentId ||
    filters.featured ||
    viewMode !== "all";

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center justify-between">
        <PageSubHeader
          title="Products"
          description="Manage your product inventory"
          breadcrumbs={[
            { title: "Dashboard", href: "/dashboard" },
            { title: "Products", current: true }
          ]}
        />

        <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
          <Link href="/dashboard/products/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
        </RoleGate>
      </div>

      <Card className="p-4">
        <CardContent className="p-0">
          <div className="flex flex-col gap-4">
            <Tabs
              value={viewMode}
              onValueChange={setViewMode}
              className="w-full"
            >
              <TabsList>
                <TabsTrigger value="all" className="cursor-pointer">
                  All Products
                </TabsTrigger>
                <TabsTrigger value="featured" className="cursor-pointer">
                  Featured
                </TabsTrigger>
                <TabsTrigger value="low-stock" className="cursor-pointer">
                  Low Stock
                </TabsTrigger>
              </TabsList>
            </Tabs>
            <div className="flex flex-wrap gap-4">
              <div className="space-y-2">
                <Label htmlFor="category-filter">Filter by Category</Label>
                <Select
                  value={filters.categoryId ?? "all"}
                  onValueChange={(value) => {
                    if (value === "all") {
                      setFilters((prev) => ({
                        ...prev,
                        categoryId: undefined
                      }));
                    } else {
                      setFilters((prev) => ({
                        ...prev,
                        categoryId: value as Id<"categories">
                      }));
                    }
                  }}
                >
                  <SelectTrigger id="category-filter">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category._id} value={category._id}>
                        {category.name}
                        <div
                          className={` rounded-full size-1.5
                            ${category.isActive ? "bg-green-500" : "bg-red-500"}
                            `}
                        />
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="brand-filter">Filter by Brand</Label>
                <Select
                  value={filters.brandId ?? "all"}
                  onValueChange={(value) => {
                    if (value === "all") {
                      setFilters((prev) => ({ ...prev, brandId: undefined }));
                    } else {
                      setFilters((prev) => ({
                        ...prev,
                        brandId: value as Id<"brands">
                      }));
                    }
                  }}
                >
                  <SelectTrigger id="brand-filter">
                    <SelectValue placeholder="All Brands" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Brands</SelectItem>
                    {brands.map((brand) => (
                      <SelectItem key={brand._id} value={brand._id}>
                        {brand.name}
                        <div
                          className={` rounded-full size-1.5
                            ${brand.isActive ? "bg-green-500" : "bg-red-500"}
                            `}
                        />
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="scent-filter">Filter by Scent</Label>
                <Select
                  value={filters.scentId ?? "all"}
                  onValueChange={(value) => {
                    if (value === "all") {
                      setFilters((prev) => ({ ...prev, scentId: undefined }));
                    } else {
                      setFilters((prev) => ({
                        ...prev,
                        scentId: value as Id<"scents">
                      }));
                    }
                  }}
                >
                  <SelectTrigger id="scent-filter">
                    <SelectValue placeholder="All Scents" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Scents</SelectItem>
                    {scents.map((scent) => (
                      <SelectItem key={scent._id} value={scent._id}>
                        {scent.name}
                        <div
                          className={` rounded-full size-1.5
                            ${scent.isActive ? "bg-green-500" : "bg-red-500"}
                            `}
                        />
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="w-full"
                  >
                    <FilterX className="mr-2 h-4 w-4" />
                    Reset Filters
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <DataTable
        columns={columns(handleDeleteProduct)}
        data={filteredProducts}
        searchKey="name"
        searchPlaceholder="Search products..."
      />
    </div>
  );
}

"use client";

import { SignInButton } from "@clerk/nextjs";
import { Authenticated, Unauthenticated } from "convex/react";

import { Container, Main, Section } from "@/components/ds";
import ProtectedContent from "@/components/protected";

export default function Page() {
  return (
    <Main>
      <Container>
        <Section>
          <Authenticated>
            <ProtectedContent />
          </Authenticated>
        </Section>
        <Section>
          <Unauthenticated>
            <SignInButton mode="modal">Sign in now</SignInButton>
          </Unauthenticated>
        </Section>
      </Container>
    </Main>
  );
}

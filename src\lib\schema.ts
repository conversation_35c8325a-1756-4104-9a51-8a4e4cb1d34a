import { z } from "zod";

import { phoneNumberRegex } from "@/utils";

export const contactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().regex(phoneNumberRegex, 'Invalid Number!').min(10, "Phone number must be at least 10 characters"),
  message: z.string().min(10, "Message must be at least 10 characters")

});

export const checkoutFormSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().regex(phoneNumberRegex, 'Invalid Number!').min(10, "Phone number must be at least 10 characters"),
  address: z.string().min(5, "Address must be at least 5 characters"),
  city: z.string().min(2, "City must be at least 2 characters"),
  country: z.string().min(1, "Please select a country"),
  postcode: z.string().min(4, "Postcode must be at least 4 characters"),
  orderNote: z.string().optional(),

});

export const newsletterFormSchema = z.object({
  email: z.string().email("Invalid email address")
});
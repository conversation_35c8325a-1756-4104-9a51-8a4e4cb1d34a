import { DEFAULT_CURRENCY } from "@/config/constants";

import { isEmpty } from "./is-empty";


type ConvertToLocaleParams = {
  amount: string | number;
  currency_code?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  locale?: string;
};

export function convertToLocale({
  amount,
  currency_code = DEFAULT_CURRENCY,
  minimumFractionDigits,
  maximumFractionDigits,
  locale = "en-US",
}: ConvertToLocaleParams) {
  return currency_code && !isEmpty(currency_code)
    ? new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currency_code,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(Number.parseFloat(amount.toString()))
    : amount.toString();
}

export function correctAmount(amount: string): number {
  return Number.parseFloat(amount) / 100;
}

import { Suspense } from "react";

import { Container, Main } from "@/components/ds";
import { Footer } from "@/components/storefront/layout/footer";
import { Navbar } from "@/components/storefront/layout/navbar/navbar";
import StoreProvider from "@/provider/store-provider";

export default function StoreLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <StoreProvider>
        <Navbar />
        <Main className="overflow-hidden">
          <Container className="py-0 sm:py-0">
            <Suspense>{children}</Suspense>
          </Container>
        </Main>
        <Footer />
      </StoreProvider>
    </>
  );
}

import { Facebook, Instagram, Linkedin, Twitter, Youtube } from "lucide-react";
import Link from "next/link";

import { Container } from "@/components/ds";
import NewsletterForm from "@/components/forms/newletter-form";
import { Typography } from "@/components/ui/typography";

import Logo from "./navbar/logo";

export const Footer = () => {
  return (
    <footer className="mt-12 py-6 border-t-2">
      <Container>
        <div className="gap-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {/* Brand Description */}
          <div className="space-y-4">
            <Logo />

            <Typography variant="smallText">
              Arome® was founded on the belief that perfume is more than just a
              necessity—it's a powerful expression of self-care and vitality.
            </Typography>
            <div className="flex gap-4 mt-4">
              <a href="#" className="text-gray-400 hover:text-primary">
                <Twitter size={18} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary">
                <Instagram size={18} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary">
                <Facebook size={18} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary">
                <Linkedin size={18} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary">
                <Youtube size={18} />
              </a>
            </div>
          </div>

          {/* Useful Links */}
          <div className="flex flex-col gap-4">
            <Typography
              as="h3"
              variant="h6"
              weight="medium"
              className="font-serif"
            >
              Useful Links
            </Typography>

            <ul className="space-y-2">
              <li>
                <Link
                  href="/account"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  My Account
                </Link>
              </li>
              <li>
                <Link
                  href="/cart"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  Shopping Cart
                </Link>
              </li>
              <li>
                <Link
                  href="/wishlist"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  My Wishlist
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  Our Store Info
                </Link>
              </li>
            </ul>
          </div>

          {/* Information */}
          <div className="flex flex-col gap-4">
            <Typography
              as="h3"
              variant="h6"
              weight="medium"
              className="font-serif"
            >
              Information
            </Typography>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/tracking"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  Order Tracking
                </Link>
              </li>
              <li>
                <Link
                  href="/wishlist"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  WishList
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-gray-600 hover:text-primary text-sm"
                >
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div className="flex flex-col gap-4">
            <Typography
              as="h3"
              variant="h6"
              weight="medium"
              className="font-serif"
            >
              Newsletter
            </Typography>
            <Typography variant="smallText" className="text-gray-600 text-sm">
              Subscribe to our newsletter to receive news on update.
            </Typography>
            <NewsletterForm />
          </div>
        </div>

        <div className="mt-12 -mb-6 pt-6 border-gray-200 border-t text-gray-600 text-sm text-center">
          <Typography variant="smallText">
            © {new Date().getFullYear()}, All Rights Reserved
          </Typography>
        </div>
      </Container>
    </footer>
  );
};

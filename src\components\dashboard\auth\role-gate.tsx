"use client";

import type { ReactNode } from "react";

import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

import { internalLinks } from "@/config/site-config";
import { useAuth } from "@/hooks/use-auth";

type RoleGateProps = {
  children: ReactNode;
  allowedRoles: string[];
  fallback?: ReactNode;
  redirectTo?: string;
};

export function RoleGate({
  children,
  allowedRoles,
  fallback = null,
  redirectTo
}: RoleGateProps) {
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (!loaded) {
    return <Loader2 className="animate-spin" />;
  }

  if (!user) {
    router.push(internalLinks.home);
    return null;
  }

  const hasAccess = allowedRoles.includes(user.role.name);

  if (!hasAccess && redirectTo) {
    router.push(redirectTo);
    return null;
  }

  if (!hasAccess) {
    return fallback;
  }

  return children;
}

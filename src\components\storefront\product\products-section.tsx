
import type { Product } from "@/types";

import { Container, Section } from "@/components/ds";
import { Typography } from "@/components/ui/typography";

import { ProductsCarousel } from "./products-carousel";
import { ProductsGrid } from "./products-grid";

type ProductsSectionProps = {
  title: string;
  description?: string;
  carousel?: boolean;
  products: Product[];
};

export function ProductsSection({
  title,
  description,
  carousel = false,
  products
}: ProductsSectionProps) {
  return (
    <Section>
      <Container className="space-y-8 mx-auto p-0 sm:p-0 w-full">
        <div className="flex flex-col items-center gap-4 mx-auto max-w-2xl">
          <Typography
            variant="h2"
            weight="semibold"
            className="font-playfair text-center"
          >
            {title}
          </Typography>
          {description && (
            <Typography variant="p" className="text-center">
              {description}
            </Typography>
          )}
        </div>
        {carousel ? (
          <ProductsCarousel products={products} />
        ) : (
          <ProductsGrid products={products} />
        )}
      </Container>
    </Section>
  );
}

"use client";

import { useQuery } from "convex/react";
import { Filter as FilterIcon } from "lucide-react";
import Link from "next/link";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";

import type { Doc } from "@/convex/_generated/dataModel";
import type { Product } from "@/types";

import { Filters } from "@/components/storefront/filters";
import Loading from "@/components/storefront/loading";
import { ProductCard } from "@/components/storefront/product/product-card";
import { buttonVariants } from "@/components/ui/button";
import { api } from "@/convex/_generated/api";
import { useStableQuery } from "@/hooks/use-stablequery";
import { cn } from "@/utils";

enum SortOptions {
  PriceAsc = "price_asc",
  PriceDesc = "price_desc",
  Oldest = "oldest",
  Newest = "newest"
}

const sortOptions = [
  { label: "Default", value: SortOptions.Newest },
  { label: "Price: Low to High", value: SortOptions.PriceAsc },
  { label: "Price: High to Low", value: SortOptions.PriceDesc },
  { label: "Oldest", value: SortOptions.Oldest },
  { label: "Newest", value: SortOptions.Newest }
];

const defaultCategory = {
  _id: "1",
  name: "All Products",
  slug: "all",
  isActive: true,
  _creationTime: Date.now()
} as Doc<"categories">;

export default function CategoryPage() {
  const { category: categorySlug } = useParams<{ category: string }>();
  const searchParams = useSearchParams();
  const selectedFilters = {
    minPrice: searchParams?.get("minPrice"),
    maxPrice: searchParams?.get("maxPrice"),
    brand: searchParams?.get("brand"),
    inStock: searchParams?.get("inStock")
  };
  let category: Doc<"categories"> | undefined;
  if (categorySlug === "all") {
    category = defaultCategory;
  } else {
    category = useQuery(api.categories.getBySlug, { slug: categorySlug });
  }
  const args =
    category === undefined
      ? "skip"
      : {
          isPublished: true,
          categoryId: categorySlug === "all" ? undefined : category?._id,
          brandSlug: selectedFilters.brand ?? undefined,
          minPrice: selectedFilters.minPrice
            ? Number(selectedFilters.minPrice)
            : undefined,
          maxPrice: selectedFilters.maxPrice
            ? Number(selectedFilters.maxPrice)
            : undefined,
          inStock: selectedFilters.inStock === "true" || undefined,
          sortBy: searchParams?.get("sort")
            ? (searchParams?.get("sort") as SortOptions)
            : undefined
        };
  const filteredProducts = useStableQuery(api.products.list, args);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const allBrands = useQuery(api.brands.list, { isActive: true });
  const router = useRouter();

  if (!category || !filteredProducts || !allBrands) {
    return <Loading />;
  }

  const query = searchParams?.get("query");

  const handleFilterChange = (
    filterType: string,
    value: string | number | boolean | undefined
  ) => {
    if (value === undefined) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete(filterType);
      router.push(`/${categorySlug}?${newSearchParams.toString()}`);
    } else {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set(filterType, value.toString());
      router.push(`/${categorySlug}?${newSearchParams.toString()}`);
    }
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const sortBy = e.target.value;
    if (sortBy === searchParams?.get("sort")) return;

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("sort", sortBy);

    router.push(`/${categorySlug}?${newSearchParams.toString()}`);
  };

  const getPageTitle = () => {
    if (query) return `Search Results: "${query}"`;
    return category.name;
  };

  return (
    <div className="py-8">
      {/* Breadcrumb */}
      <div className="flex items-center mb-8 text-sm">
        <Link href="/" className="text-gray-500 hover:text-purple-500">
          Home
        </Link>
        <span className="mx-2">/</span>
        <span>{getPageTitle()}</span>
      </div>

      <>
        {/* Mobile filter button */}
        <div className="lg:hidden mb-4">
          <button
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="flex justify-center items-center space-x-2 bg-white shadow-sm py-3 border rounded-lg w-full"
          >
            <FilterIcon size={18} />
            <span>{showMobileFilters ? "Hide Filters" : "Show Filters"}</span>
          </button>
        </div>

        <div className="flex lg:flex-row flex-col">
          {/* Filters - Sidebar */}
          <div
            className={`${
              showMobileFilters ? "block" : "hidden"
            } lg:block lg:w-1/4 pr-0 lg:pr-6 mb-6 lg:mb-0`}
          >
            <div className="top-24 sticky">
              <Filters
                allBrands={allBrands}
                selectedFilters={selectedFilters}
                handleFilterChange={handleFilterChange}
              />
            </div>
          </div>

          {/* Products Grid */}
          <div className="lg:w-3/4">
            {/* Page title and sorting */}
            <div className="flex md:flex-row flex-col justify-between items-start md:items-center mb-6">
              <h1 className="mb-4 md:mb-0 font-serif text-2xl">
                {getPageTitle()}
              </h1>
              <div className="flex items-center">
                <label className="mr-2 text-sm">Sort By:</label>
                <select
                  className="p-2 border rounded text-sm"
                  onChange={handleSortChange}
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Products grid */}
            {filteredProducts?.products.length > 0 ? (
              <div className="gap-6 grid grid-cols-2 md:grid-cols-3">
                {filteredProducts?.products.map((product: Product) => (
                  <ProductCard key={product._id} product={product} />
                ))}
              </div>
            ) : (
              <div className="bg-white shadow-sm py-12 rounded-lg text-center">
                <h2 className="mb-4 font-medium text-xl">No products found</h2>
                <p className="mb-6 text-gray-500">
                  Try adjusting your search or filter to find what you're
                  looking for.
                </p>
                <Link
                  href="/category"
                  className={cn(buttonVariants({ variant: "default" }))}
                >
                  View All Products
                </Link>
              </div>
            )}

            {/* Pagination - simplified for the example */}
            {filteredProducts?.products.length > 0 && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center space-x-2">
                  <button className="flex justify-center items-center border hover:border-purple-500 rounded w-10 h-10 hover:text-purple-500">
                    &laquo;
                  </button>
                  <button className="flex justify-center items-center bg-purple-500 rounded w-10 h-10 text-white">
                    1
                  </button>
                  <button className="flex justify-center items-center border hover:border-purple-500 rounded w-10 h-10 hover:text-purple-500">
                    2
                  </button>
                  <button className="flex justify-center items-center border hover:border-purple-500 rounded w-10 h-10 hover:text-purple-500">
                    &raquo;
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    </div>
  );
}

"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>con, ImageIcon, UploadIcon, XIcon } from "lucide-react";

import type { FileWithPreview } from "@/hooks/use-file-upload";

import { Button } from "@/components/ui/button";
import { ACCEPT_IMAGE_TYPES, MAX_IMAGE_SIZE_MB } from "@/config/constants";
import { useFileUpload } from "@/hooks/use-file-upload";

type Props = {
  maxSizeMB?: number;
  maxFiles?: number;
  accept?: string;
  onFilesChange?: (files: FileWithPreview[]) => void;
  onFilesAdded?: (files: FileWithPreview[]) => void;
};

export default function MultipleFileUpload({
  maxSizeMB = MAX_IMAGE_SIZE_MB,
  accept = ACCEPT_IMAGE_TYPES,
  maxFiles = 6,
  onFilesChange,
  onFilesAdded
}: Props) {
  const maxSize = maxSizeMB * 1024 * 1024; // 5MB default

  const [
    { files, isDragging, errors },
    {
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
      openFileDialog,
      removeFile,
      getInputProps
    }
  ] = useFileUpload({
    accept,
    maxSize,
    multiple: true,
    maxFiles,
    onFilesChange,
    onFilesAdded
  });

  return (
    <div className="flex flex-col gap-2">
      {/* Drop area */}
      <div
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        data-dragging={isDragging || undefined}
        data-files={files.length > 0 || undefined}
        className="relative flex flex-col not-data-[files]:justify-center items-center data-[dragging=true]:bg-accent/50 p-4 border border-input has-[input:focus]:border-ring border-dashed rounded-xl has-[input:focus]:ring-[1px] has-[input:focus]:ring-ring/50 min-h-52 overflow-hidden transition-colors"
      >
        <input
          {...getInputProps()}
          className="sr-only"
          aria-label="Upload image file"
        />
        {files.length > 0 ? (
          <div className="flex flex-col gap-3 w-full">
            <div className="flex justify-between items-center gap-2">
              <h3 className="font-medium text-sm truncate">
                Uploaded Files ({files.length})
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={openFileDialog}
                disabled={files.length >= maxFiles}
              >
                <UploadIcon
                  className="opacity-60 -ms-0.5 size-3.5"
                  aria-hidden="true"
                />
                Add more
              </Button>
            </div>

            <div className="gap-4 grid grid-cols-2 md:grid-cols-3">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="group relative bg-accent rounded-md aspect-square overflow-hidden"
                >
                  <img
                    src={file.preview}
                    alt={file.file.name}
                    className="rounded-[inherit] size-full object-cover"
                  />
                  <div className="right-0 bottom-0 left-0 absolute bg-black bg-opacity-50 p-2 text-white text-xs transition-transform translate-y-full group-hover:translate-y-0">
                    <p className="truncate">{file.file.name}</p>
                    <p>{(file.file.size / (1024 * 1024)).toFixed(2)} MB</p>
                  </div>
                  <Button
                    type="button"
                    onClick={() => removeFile(file.id)}
                    size="icon"
                    className="-top-0 -right-0 absolute shadow-none border-2 border-background focus-visible:border-background rounded-full size-6"
                    aria-label="Remove image"
                  >
                    <XIcon className="size-3.5" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="flex flex-col justify-center items-center px-4 py-3 text-center">
            <div
              className="flex justify-center items-center bg-background mb-2 border rounded-full size-11 shrink-0"
              aria-hidden="true"
            >
              <ImageIcon className="opacity-60 size-4" />
            </div>
            <p className="mb-1.5 font-medium text-sm">Drop your images here</p>
            <p className="text-muted-foreground text-xs">
              SVG, PNG, JPG or GIF (max. {maxSizeMB}MB)
            </p>
            <Button
              type="button"
              variant="outline"
              className="mt-4"
              onClick={openFileDialog}
            >
              <UploadIcon className="opacity-60 -ms-1" aria-hidden="true" />
              Select images
            </Button>
          </div>
        )}
      </div>

      {errors.length > 0 && (
        <div
          className="flex items-center gap-1 text-destructive text-xs"
          role="alert"
        >
          <AlertCircleIcon className="size-3 shrink-0" />
          <span>{errors[0]}</span>
        </div>
      )}
    </div>
  );
}

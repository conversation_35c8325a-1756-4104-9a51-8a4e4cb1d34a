import Link from "next/link";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  She<PERSON><PERSON>eader,
  SheetTitle
} from "@/components/ui/sheet";
import { Typography } from "@/components/ui/typography";
import { internalLinks } from "@/config/site-config";
import { useCart } from "@/hooks/use-cart";
import { convertToLocale } from "@/utils/money";

import { CartItem } from "./car-item";

export const CartSheet = () => {
  const { cartItems, totalAmount, isOpen, closeCart } = useCart();
  const router = useRouter();

  const handleCheckout = () => {
    closeCart();
    router.push(internalLinks.checkout);
  };

  return (
    <Sheet open={isOpen} onOpenChange={closeCart}>
      <SheetContent className="gap-2 w-full max-w-md">
        <SheetHeader className="pb-1">
          <Typography asChild as="h2" variant="h5" className="font-playfair">
            <SheetTitle>
              {cartItems.length === 0
                ? "Your cart is empty"
                : "Items added to your cart"}
            </SheetTitle>
          </Typography>
        </SheetHeader>

        {cartItems.length === 0 ? (
          <div className="px-2 py-8 text-center">
            <Typography variant="mutedText" className="mb-4">
              Your shopping cart is empty
            </Typography>
            <Button asChild variant="secondary">
              <Link href={internalLinks.shop} onClick={closeCart}>
                Continue Shopping
              </Link>
            </Button>
          </div>
        ) : (
          <>
            {/* Cart items */}
            <div className="space-y-4 px-4 h-full max-h-[calc(100%-60px-55px)] overflow-x-hidden">
              {cartItems.map((item) => (
                <CartItem key={item._id} item={item} />
              ))}
            </div>

            {/* Footer */}
            <SheetFooter className="space-y-2 p-2 pt-6 border-t border-t-foreground/80">
              {/* Subtotal */}
              <div className="flex justify-between items-center font-medium text-lg">
                <Typography variant="p" weight="bold">
                  Subtotal
                </Typography>
                <Typography variant="span" className="text-right">
                  {convertToLocale({ amount: totalAmount })}
                </Typography>
              </div>

              {/* Checkout button */}
              <Button className="w-full" onClick={handleCheckout}>
                CHECK OUT
              </Button>
            </SheetFooter>
          </>
        )}
      </SheetContent>
    </Sheet>
  );
};

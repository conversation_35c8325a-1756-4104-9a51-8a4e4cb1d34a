"use client";

import { Arrow<PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { BrandForm } from "../_components/brand-form";

export default function NewBrandPage() {
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  return (
    <div className="flex flex-col gap-4 p-4">
      <PageSubHeader
        title="Add New Brand"
        description="Create a new brand for your products"
        breadcrumbs={[
          { title: "Dashboard", href: "/dashboard" },
          { title: "Brands", href: "/dashboard/brands" },
          { title: "New Brand", current: true }
        ]}
      >
        <Button variant="outline" asChild>
          <Link href="/dashboard/brands">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Brands
          </Link>
        </Button>
      </PageSubHeader>

      <RoleGate
        allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]}
        redirectTo="/dashboard/brands"
      >
        <BrandForm />
      </RoleGate>
    </div>
  );
}

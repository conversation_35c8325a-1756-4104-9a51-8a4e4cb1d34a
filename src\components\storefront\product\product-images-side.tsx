import type { Dispatch, SetStateAction } from "react";

import Image from "next/image";
import { useCallback } from "react";

import type { CarouselApi } from "@/components/ui/carousel";
import type { Image as ImageTpe } from "@/types";

import { Carousel, CarouselContent } from "@/components/ui/carousel";
import { cn } from "@/utils";

type ProductImagesSideProps = {
  images: ImageTpe[];
  api: CarouselApi;
  setThumbsApi: Dispatch<SetStateAction<CarouselApi>>;
  current: number;
  className?: string;
};

export function ProductImagesSide({
  className,
  images,
  api,
  setThumbsApi,
  current
}: ProductImagesSideProps) {
  const onThumbClick = useCallback(
    (index: number) => {
      api?.scrollTo(index);
    },
    [api]
  );

  return (
    <div className={className}>
      <Carousel
        className="md:top-[100px] md:sticky my-4 md:my-0"
        orientation="vertical"
        setApi={setThumbsApi}
        opts={{
          skipSnaps: true,
          watchDrag: false
        }}
      >
        <CarouselContent
          className={cn("flex-row justify-center gap-1 mt-0 w-full  p-0.5")}
        >
          {images.map((image, index) => (
            <div
              className={cn(
                "w-fit",
                index === (current === 0 ? current : current - 1) &&
                  "border border-black"
              )}
              key={image._id}
              onMouseEnter={() => onThumbClick(index)}
            >
              <Image
                alt="product image"
                src={image.imageUrl}
                width={64}
                height={64}
                sizes="64px"
                className="aspect-square"
              />
            </div>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
}

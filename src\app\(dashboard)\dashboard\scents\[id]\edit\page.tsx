"use client";

import { useQuery } from "convex/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { ScentForm } from "../../_components/scent-form";

export default function EditScentPage() {
  const { id: scentId } = useParams();
  const scent = useQuery(api.scents.get, { id: scentId as Id<"scents"> });
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  if (!scent) {
    return <Loading />;
  }

  return (
    <RoleGate
      allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]}
      redirectTo="/dashboard/scents"
    >
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center justify-between">
          <PageSubHeader
            title={`Edit ${scent.name}`}
            description="Update scent information"
            breadcrumbs={[
              { title: "Dashboard", href: "/dashboard" },
              { title: "Scents", href: "/dashboard/scents" },
              { title: scent.name, href: `/dashboard/scents/${scent._id}` },
              { title: "Edit", current: true }
            ]}
          />
          <Button variant="outline" asChild>
            <Link href={`/dashboard/scents/${scent._id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Scent
            </Link>
          </Button>
        </div>

        <ScentForm initialData={scent} scentId={scent._id} />
      </div>
    </RoleGate>
  );
}

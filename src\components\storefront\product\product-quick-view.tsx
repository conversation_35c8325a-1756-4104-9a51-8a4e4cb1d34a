"use client";

import { <PERSON>UpR<PERSON>, Minus, Plus } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import type { Product } from "@/types";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Typography } from "@/components/ui/typography";
import { internalLinks } from "@/config/site-config";
import { useCart } from "@/hooks/use-cart";
import { convertToLocale } from "@/utils/money";

import { ProductImages } from "./product-images";

type ProductQuickViewProps = {
  product: Product;
  trigger: React.ReactNode;
};
export function ProductQuickView({ product, trigger }: ProductQuickViewProps) {
  const { addItem } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [open, setOpen] = useState(false);
  const router = useRouter();

  const handleIncrement = () => {
    if (quantity < product.stock) {
      setQuantity(quantity + 1);
    }
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleAddToCart = () => {
    addItem(product, quantity);
    toast.success("Added to cart");
  };

  const handleBuyNow = () => {
    addItem(product, quantity);
    setOpen(false);
    router.push(internalLinks.checkout);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="p-0 rounded-xl sm:max-w-[90vw] lg:max-w-[1024px]">
        <DialogTitle className="sr-only">{product.name} quick view</DialogTitle>
        <div className="gap-8 grid grid-cols-1 md:grid-cols-2 p-8 w-full max-h-[90vh] overflow-y-auto">
          {/* Product image */}
          <div className="aspect-square">
            <ProductImages images={product.images} />
          </div>

          {/* Product details */}
          <div>
            <Typography variant="h2" className="mb-4">
              {product.name}
            </Typography>
            {/* Price row */}
            <div className="flex items-center gap-2 mb-4">
              {product.sale?.active ? (
                <>
                  <Typography variant="price" className="text-primary">
                    {convertToLocale({ amount: product.sale.price })}
                  </Typography>
                  <Typography variant="mutedText" className="line-through">
                    {convertToLocale({ amount: product.regularPrice })}
                  </Typography>
                  <span className="bg-primary ml-2 px-2 py-1 rounded-full font-bold text-primary-foreground text-xs">
                    SALE
                  </span>
                  <span className="bg-black ml-2 px-2 py-1 rounded-full font-bold text-primary-foreground text-sm">
                    -{product.sale.discountPercentage}%
                  </span>
                </>
              ) : (
                <Typography variant="price" className="text-primary">
                  {convertToLocale({ amount: product.price })}
                </Typography>
              )}
            </div>

            {/* Quantity selector */}
            <div className="mb-4 lg:mb-8">
              <Typography variant="body" weight="medium" className="mb-2">
                Quantity
              </Typography>
              <div className="flex gap-4 lg:gap-8">
                <div className="flex items-center border rounded w-fit">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleDecrement}
                    className="border-r rounded-none"
                    disabled={quantity <= 1}
                  >
                    <Minus size={16} />
                  </Button>
                  <input
                    type="text"
                    value={quantity}
                    readOnly
                    className="rounded-none focus:outline-none w-9 text-center"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleIncrement}
                    className="border-l rounded-none"
                    disabled={quantity >= product.stock}
                  >
                    <Plus size={16} />
                  </Button>
                </div>

                <Button
                  onClick={handleAddToCart}
                  className="flex-1 uppercase"
                  disabled={product.stock === 0}
                >
                  ADD TO CART
                </Button>
              </div>
            </div>

            {/* Action buttons */}
            <div className="space-y-4 mb-4">
              <Button
                onClick={handleBuyNow}
                variant="secondary"
                className="bg-black hover:bg-gray-800 w-full text-white uppercase"
                disabled={product.stock === 0}
              >
                BUY IT NOW
              </Button>
            </div>

            <Link
              href={`/products/${product.slug}`}
              className="inline-flex items-center mt-4 font-semibold text-primary text-sm hover:underline"
            >
              View full details <ArrowUpRight className="ml-1" size={16} />
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

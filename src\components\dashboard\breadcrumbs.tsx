import { HomeIcon } from "lucide-react";
import React from "react";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList
} from "@/components/ui/breadcrumb";
import { cn } from "@/utils";

type BreadcrumbItemProp = {
  title: string;
  href?: string;
  current?: boolean;
};

type BreadcrumbsProps = {
  breadcrumbs: BreadcrumbItemProp[];
  className?: string;
};

export function Breadcrumbs({ breadcrumbs, className }: BreadcrumbsProps) {
  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {breadcrumbs.map(({ title, href, current }) => {
          // const isLast = idx + 1 === breadcrumbs.length;

          return (
            <React.Fragment key={title + href}>
              <BreadcrumbItem aria-current={current ? "page" : undefined}>
                <BreadcrumbLink
                  href={href}
                  className={cn(
                    "flex items-center gap-2 text-sm text-neutral-500 hover:text-primary",
                    current && "font-medium text-primary pointer-events-none"
                  )}
                >
                  {title.toLowerCase().includes("dashboard") && (
                    <HomeIcon className="w-4 h-4" />
                  )}
                  {title}
                </BreadcrumbLink>
              </BreadcrumbItem>
              {!current && <span>/</span>}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

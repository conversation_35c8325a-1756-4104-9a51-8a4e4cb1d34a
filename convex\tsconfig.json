{
  /* This TypeScript project config describes the environment that
   * Convex functions run in and is used to typecheck them.
   * You can modify it, but some settings required to use Convex.
   */
  "compilerOptions": {

    /* These compiler options are required by Convex */
    "target": "ESNext",
    "jsx": "react-jsx",
    "lib": ["ES2021", "dom"],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    /* These settings are not required by Convex and can be modified. */
    "allowJs": true,
    "strict": true,
    "noEmit": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": ["./**/*"],
  "exclude": ["./_generated"]
}

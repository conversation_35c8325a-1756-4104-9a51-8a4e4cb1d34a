import type { ClassValue } from "clsx";

import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function slugify(string: string) {
  return string.replace(/^\s+|\s+$/g, "") // remove leading/trailing spaces
    .toLowerCase()
    .replace(/[^a-z0-9\s-]+/g, "") // remove invalid chars except spaces and hyphens
    .replace(/\s+/g, "-") // convert spaces to hyphens
    .replace(/-+/g, "-") // remove duplicate hyphens
    .replace(/^-+|-+$/g, "") // remove leading/trailing hyphen
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    timeZone: "UTC",
    hour: "numeric",
    minute: "numeric",
  }).format(new Date(date));
}

/* eslint-disable regexp/no-super-linear-backtracking, regexp/no-misleading-capturing-group, prefer-regex-literals  */
export const phoneNumberRegex = new RegExp(/^(\+?[\s0-9]+)?(\d{3}|\(?\d+\))?(-?\s?\d)+$/)

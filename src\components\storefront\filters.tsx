import { ChevronDown, ChevronUp, Filter } from "lucide-react";
import React, { useState } from "react";

import type { Doc } from "@/convex/_generated/dataModel";

import { Slider } from "@/components/ui/slider";

const priceRange = {
  min: 0,
  max: 1000
};

type FiltersProps = {
  allBrands: Doc<"brands">[];
  selectedFilters: {
    minPrice: string | null;
    maxPrice: string | null;
    brand: string | null;
    inStock: string | null;
  };
  handleFilterChange: (
    filterType: string,
    value: string | number | boolean | undefined
  ) => void;
};

export const Filters = ({
  allBrands,
  selectedFilters,
  handleFilterChange
}: FiltersProps) => {
  const [showFilters, setShowFilters] = useState(true);

  const [minPrice, setMinPrice] = useState(0);
  const [maxPrice, setMaxPrice] = useState(1000);

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setMinPrice(value);
    if (value <= maxPrice) {
      handleFilterChange("minPrice", value.toString());
    }
  };

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setMaxPrice(value);
    if (value >= minPrice) {
      handleFilterChange("maxPrice", value.toString());
    }
  };

  const handleSliderChange = (values: number[]) => {
    setMinPrice(values[0]);
    setMaxPrice(values[1]);
    handleFilterChange("minPrice", values[0].toString());
    handleFilterChange("maxPrice", values[1].toString());
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 lg:p-6">
      {/* Mobile toggle button */}
      <div className="flex items-center justify-between mb-6 lg:hidden">
        <h3 className="text-lg font-medium">Filters</h3>
        <button
          onClick={toggleFilters}
          className="flex items-center space-x-2 text-sm text-purple-500"
        >
          <Filter size={18} />
          <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
          {showFilters ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </button>
      </div>

      <div className={`${showFilters ? "block" : "hidden"} lg:block space-y-6`}>
        <div className="mb-6">
          <h3 className="font-medium mb-4">Availability</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-2"
                checked={selectedFilters.inStock === "true"}
                onChange={() =>
                  handleFilterChange(
                    "inStock",
                    selectedFilters.inStock === "true" ? "false" : "true"
                  )
                }
              />
              <span>In Stock</span>
            </label>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="font-medium mb-4">Price</h3>
          <Slider
            value={[minPrice, maxPrice]}
            min={priceRange.min}
            max={priceRange.max}
            step={1}
            onValueChange={handleSliderChange}
            className="mb-4"
          />
          <div className="flex items-center justify-between gap-2">
            <div className="flex-1">
              <label className="text-xs text-gray-500">Min</label>
              <input
                type="number"
                min={priceRange.min}
                max={maxPrice}
                value={minPrice}
                onChange={handleMinPriceChange}
                className="w-full border rounded px-2 py-1 text-sm"
              />
            </div>
            <span className="text-gray-500">-</span>
            <div className="flex-1">
              <label className="text-xs text-gray-500">Max</label>
              <input
                type="number"
                min={minPrice}
                max={priceRange.max}
                value={maxPrice}
                onChange={handleMaxPriceChange}
                className="w-full border rounded px-2 py-1 text-sm"
              />
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="font-medium mb-4">Brands</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {allBrands.map((brand) => (
              <label key={brand._id} className="flex items-center">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={selectedFilters.brand === brand.slug}
                  onChange={() => {
                    if (selectedFilters.brand === brand.slug) {
                      handleFilterChange("brand", undefined);
                    } else {
                      handleFilterChange("brand", brand.slug);
                    }
                  }}
                />
                <span className="capitalize">{brand.name}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

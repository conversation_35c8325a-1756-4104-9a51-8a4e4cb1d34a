"use client";

import type { z } from "zod";

import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { subscribeNewsletter } from "@/actions/newsletter.actions";
import { newsletterFormSchema } from "@/lib/schema";

import { Button } from "../ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from "../ui/form";
import { Input } from "../ui/input";

export default function NewsletterForm() {
  const form = useForm<z.infer<typeof newsletterFormSchema>>({
    resolver: zodResolver(newsletterFormSchema),
    defaultValues: {}
  });

  const onSubmit = async (data: z.infer<typeof newsletterFormSchema>) => {
    const result = await subscribeNewsletter(data);

    if (result.success) {
      toast.success(result.message);
      form.reset();
    } else {
      toast.error(result.message);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex justify-between gap-2"
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Enter your email"
                  disabled={form.formState.isSubmitted}
                  className="px-4 py-2 rounded-none placeholder:text-muted-foreground/50"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          size="icon"
          disabled={form.formState.isSubmitting || form.formState.isSubmitted}
          className="rounded-none"
        >
          <ArrowRight className="size-4" />
        </Button>
      </form>
    </Form>
  );
}

import { Minus, Plus } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

import type { CartItem as CartItemType } from "@/lib/context/cart";

import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { useCart } from "@/hooks/use-cart";
import { convertToLocale } from "@/utils/money";

type CartItemProps = {
  item: CartItemType;
  showControls?: boolean;
};

export const CartItem: React.FC<CartItemProps> = ({
  item,
  showControls = true
}) => {
  const { updateItemQuantity, removeItem } = useCart();

  const handleIncrement = () => {
    updateItemQuantity(item._id, item.quantity + 1);
  };

  const handleDecrement = () => {
    if (item.quantity > 1) {
      updateItemQuantity(item._id, item.quantity - 1);
    }
  };

  const handleRemove = () => {
    removeItem(item._id);
  };

  const totalPrice = item.price * item.quantity;

  return (
    <div className="flex py-4 border-gray-200 last:border-0 border-b">
      {/* Product image */}
      <Link href={`/products/${item.slug}`} className="flex-shrink-0 w-20 h-20">
        <Image
          width={80}
          height={80}
          src={item.images[0].imageUrl}
          alt={item.name}
          className="size-full object-cover aspect-square"
        />
      </Link>

      {/* Product details */}
      <div className="flex-grow ml-4">
        <Typography variant="mutedText" className="text-xs uppercase">
          {item.brand.name}
        </Typography>
        <Typography
          asChild
          as="h2"
          variant="p"
          weight="medium"
          className="font-playfair"
        >
          <Link href={`/products/${item.slug}`}>{item.name}</Link>
        </Typography>
        <div className="flex justify-between items-center">
          <Typography weight="medium" className="text-primary">
            {convertToLocale({ amount: totalPrice })}
          </Typography>

          {showControls ? (
            <div className="flex items-center space-x-4">
              {/* Quantity control */}
              <div className="flex items-center border rounded">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDecrement}
                  className="rounded-none"
                  disabled={item.quantity <= 1}
                >
                  <Minus className="size-4" />
                </Button>
                <Typography className="p-2 text-xs">{item.quantity}</Typography>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleIncrement}
                  className="rounded-none"
                >
                  <Plus className="size-4" />
                </Button>
              </div>

              {/* Remove button */}
              <Button
                variant="link"
                size="sm"
                onClick={handleRemove}
                className="hover:text-destructive underline"
              >
                <Typography className="hover:text-destructive text-xs">
                  Remove
                </Typography>
              </Button>
            </div>
          ) : (
            <Typography variant="smallText" weight="medium">
              Qty: {item.quantity}
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
};

"use client";

import { useQuery } from "convex/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { CategoryForm } from "../../_components/category-form";

export default function EditCategoryPage() {
  const { id: categoryId } = useParams();
  const category = useQuery(api.categories.get, {
    id: categoryId as Id<"categories">
  });
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (!category) {
    return <Loading />;
  }

  return (
    <RoleGate
      allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]}
      redirectTo="/dashboard/categories"
    >
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center justify-between">
          <PageSubHeader
            title={`Edit ${category.name}`}
            description="Update category information"
            breadcrumbs={[
              { title: "Dashboard", href: "/dashboard" },
              { title: "Categories", href: "/dashboard/categories" },

              {
                title: category.name,
                href: `/dashboard/categories/${category._id}`
              },
              { title: "Edit", current: true }
            ]}
          />
          <Button variant="outline" asChild>
            <Link href={`/dashboard/categories/${category._id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Category
            </Link>
          </Button>
        </div>

        <CategoryForm initialData={category} categoryId={category._id} />
      </div>
    </RoleGate>
  );
}

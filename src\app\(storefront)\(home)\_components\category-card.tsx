import Image from "next/image";
import Link from "next/link";

import { Typography } from "@/components/ui/typography";

type CategoryCardProps = {
  title: string;
  image?: string;
  slug: string;
};

const CategoryCard = ({ title, image, slug }: CategoryCardProps) => {
  return (
    <Link href={`/${slug}`}>
      <div className="flex flex-col items-center gap-2">
        <div className="relative rounded-lg w-full aspect-square overflow-hidden">
          <Image
            width={400}
            height={400}
            src={image ?? "/category-men.jpg"}
            alt={title}
            className="size-full object-cover hover:scale-125 transition-transform duration-700"
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          />
        </div>
        <div className="flex justify-center items-center">
          <Typography
            as="h3"
            variant="body"
            weight="semibold"
            className="font-playfair text-center"
          >
            {title}
          </Typography>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;

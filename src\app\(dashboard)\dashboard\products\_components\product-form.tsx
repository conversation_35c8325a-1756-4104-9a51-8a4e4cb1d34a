"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "convex/react";
import { Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import type { Doc, Id } from "@/convex/_generated/dataModel";

import { uploadImage } from "@/actions/image.actions";
import MultipleFileUpload from "@/components/multiple-file-upload";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { api } from "@/convex/_generated/api";
import { slugify } from "@/utils";
import { tryCatch } from "@/utils/try-catch";

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Product name must be at least 2 characters."
  }),
  slug: z.string().min(2, {
    message: "Category slug must be at least 2 characters."
  }),
  shortDescription: z.string().min(10, {
    message: "Short description must be at least 10 characters."
  }),
  description: z.string().min(15, {
    message: "Description must be at least 15 characters."
  }),
  regularPrice: z.coerce.number().positive({
    message: "Regular price must be a positive number."
  }),
  sale: z
    .object({
      active: z.boolean(),
      price: z.coerce.number(),
      discountPercentage: z.coerce.number(),
      originalPrice: z.coerce.number().positive(),
      startDate: z.coerce.number(),
      endDate: z.coerce.number().optional()
    })
    .refine(
      (data) => {
        if (!data.active) return true;
        if (data.price > 0) return true;
        return data.price < 1;
      },
      {
        message: "Sale price must be a positive number.",
        path: ["price"]
      }
    )
    .refine(
      (data) => {
        if (!data.active) return true;
        if (data.discountPercentage > 0 && data.discountPercentage < 99)
          return true;
        return data.discountPercentage < 1 || data.discountPercentage > 99;
      },
      {
        message: "Discount percentage must be greater than 0 and less than 100",
        path: ["discountPercentage"]
      }
    )
    .refine(
      (data) => {
        if (!data.active) return true;
        if (!data.startDate || !data.endDate) return true;
        return data.startDate <= data.endDate;
      },
      {
        message: "End date must be greater than or equal to start date",
        path: ["startDate"]
      }
    ),
  stock: z.coerce.number().int().nonnegative({
    message: "Stock must be a non-negative integer."
  }),
  categoryId: z.string().min(1, {
    message: "Please select a category."
  }),
  brandId: z.string().min(1, {
    message: "Please select a brand."
  }),
  scentId: z.string().optional(),
  featured: z.boolean(),
  isPublished: z.boolean(),
  isNew: z.boolean(),
  images: z.array(z.any()),
  imagesFiles: z.array(z.any()).min(1, {
    message: "Please upload at least one image."
  })
});

type FormValues = z.infer<typeof formSchema>;

type ProductFormProps = {
  initialData?: Partial<FormValues> & {
    images?: Doc<"images">[];
  };
  productId?: string;
};

export function ProductForm({ initialData, productId }: ProductFormProps) {
  const router = useRouter();
  const categories = useQuery(api.categories.list, { isActive: undefined });
  const brands = useQuery(api.brands.list, { isActive: undefined });
  const scents = useQuery(api.scents.list, { isActive: undefined });
  const createProduct = useMutation(api.products.create);
  const updateProduct = useMutation(api.products.update);
  const createProductImages = useMutation(api.images.create);

  const defaultValues: Partial<FormValues> = {
    name: "",
    shortDescription: "",
    description: "",
    regularPrice: 0,
    stock: 0,
    categoryId: "",
    brandId: "",
    scentId: undefined,
    featured: false,
    isPublished: true,
    isNew: true,
    images: [],
    imagesFiles: initialData?.images,
    sale: {
      active: false,
      price: 0,
      discountPercentage: 0,
      originalPrice: initialData?.regularPrice || 0,
      startDate: Date.now(),
      endDate: undefined
    },
    ...initialData
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues
  });
  const isSubmitting = form.formState.isSubmitting;

  const onSubmit = async (values: FormValues) => {
    // Create or update product
    if (!productId) {
      // Create new product
      const {
        data: createdProduct,
        success,
        error
      } = await tryCatch(
        createProduct({
          name: values.name,
          slug: values.slug,
          shortDescription: values.shortDescription,
          description: values.description,
          regularPrice: values.regularPrice,
          sale: values.sale,
          stock: values.stock,
          categoryId: values.categoryId as Id<"categories">,
          brandId: values.brandId as Id<"brands">,
          scentId: values.scentId as Id<"scents"> | undefined
        })
      );

      if (success) {
        if (values.imagesFiles && values.imagesFiles.length > 0) {
          await Promise.all(
            values.imagesFiles.map(async (imageFile, index) => {
              if (imageFile instanceof File) {
                const {
                  data: imageData,
                  success,
                  message
                } = await uploadImage(imageFile);

                if (!success || !imageData) {
                  toast.error(
                    message || `Failed to upload image ${imageFile.name}`
                  );
                  return;
                }

                await createProductImages({
                  imageUrl: imageData.imageUrl,
                  imageId: imageData.storageId as Id<"_storage">,
                  isPrimary: index === 0,
                  productId: createdProduct._id
                });
              }
            })
          );
        }
        toast.success("Product created successfully");
        router.push("/dashboard/products");
      } else {
        console.error(error);
        toast.error("Error creating product", {
          description: error?.message || "Please try again"
        });
      }
    } else {
      // Update existing product
      const {
        data: updatedProduct,
        success,
        error
      } = await tryCatch(
        updateProduct({
          id: productId as Id<"products">,
          name: values.name,
          slug: values.slug,
          shortDescription: values.shortDescription,
          description: values.description,
          regularPrice: values.regularPrice,
          sale: values.sale,
          stock: values.stock,
          categoryId: values.categoryId as Id<"categories">,
          brandId: values.brandId as Id<"brands">,
          scentId: values.scentId as Id<"scents"> | undefined,
          isPublished: values.isPublished,
          isNew: values.isNew,
          featured: values.featured
        })
      );

      if (success) {
        if (values.imagesFiles && values.imagesFiles.length > 0) {
          await Promise.all(
            values.imagesFiles.map(async (imageFile, index) => {
              if (imageFile instanceof File) {
                const {
                  data: imageData,
                  success,
                  message
                } = await uploadImage(imageFile);

                if (!success || !imageData) {
                  toast.error(
                    message || `Failed to upload image ${imageFile.name}`
                  );
                  return;
                }

                await createProductImages({
                  imageUrl: imageData.imageUrl,
                  imageId: imageData.storageId as Id<"_storage">,
                  isPrimary: index === 0,
                  productId: updatedProduct._id
                });
              }
            })
          );
        }
        toast.success("Product updated successfully");
        router.push("/dashboard/products");
      } else {
        console.error(error);
        toast.error("Error updating product", {
          description: error?.message || "Please try again"
        });
      }
    }
  };

  const onErrors = (errors: any) => {
    console.error(errors);
    const formErrors = Object.values(errors)
      .map((error: any) => error.message)
      .filter(Boolean);
    formErrors.length > 0 && toast.error(formErrors.join("\n"));
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit, onErrors)}
        className="space-y-6"
      >
        <Card>
          <CardContent className="pt-6">
            <div className="gap-6 grid md:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Product name"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          form.setValue("slug", slugify(e.target.value));
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories ? (
                          categories.map((category) => (
                            <SelectItem key={category._id} value={category._id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="px-2 py-1 text-sm">
                            No category.&nbsp;
                            <Link
                              href="/dashboard/categories/new"
                              target="_blank"
                              className="text-blue-500 hover:underline"
                            >
                              Create
                            </Link>
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="brandId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a brand" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {brands ? (
                          brands.map((brand) => (
                            <SelectItem key={brand._id} value={brand._id}>
                              {brand.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="px-2 py-1 text-sm">
                            No brand.&nbsp;
                            <Link
                              href="/dashboard/brands/new"
                              target="_blank"
                              className="text-blue-500 hover:underline"
                            >
                              Create
                            </Link>
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Scent</FormLabel>
                    <FormControl>
                      <div className="flex flex-wrap gap-4">
                        {scents ? (
                          scents.map((scent) => (
                            <div
                              key={scent._id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`scent-${scent._id}`}
                                checked={field.value === scent._id}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    field.onChange(scent._id);
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                              />
                              <label
                                htmlFor={`scent-${scent._id}`}
                                className="text-sm"
                              >
                                {scent.name}
                              </label>
                            </div>
                          ))
                        ) : (
                          <div className="px-2 py-1 text-sm">
                            No scent.&nbsp;
                            <Link
                              href="/dashboard/scents/new"
                              target="_blank"
                              className="text-blue-500 hover:underline"
                            >
                              Create
                            </Link>
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="regularPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Regular Price ($)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e.target.valueAsNumber);
                          form.setValue(
                            "sale.originalPrice",
                            e.target.valueAsNumber
                          );
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="stock"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stock</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="shortDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Short Description</FormLabel>
                      <FormControl>
                        <Textarea
                          rows={2}
                          placeholder="Short product description"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          rows={5}
                          placeholder="Product description"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="md:col-span-2">
                {initialData?.images && initialData.images.length > 0 ? (
                  <div className="space-y-4">
                    <FormLabel>Product Images</FormLabel>
                    <div className="gap-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                      {initialData.images.map((image) => (
                        <ProductImageCard key={image._id} image={image} />
                      ))}
                    </div>
                    <FormField
                      control={form.control}
                      name="imagesFiles"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Add More Images</FormLabel>
                          <FormControl>
                            <MultipleFileUpload
                              onFilesChange={(files) => {
                                field.onChange(files.map((f) => f.file));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ) : (
                  <FormField
                    control={form.control}
                    name="imagesFiles"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Images</FormLabel>
                        <FormControl>
                          <MultipleFileUpload
                            onFilesChange={(files) => {
                              field.onChange(files.map((f) => f.file));
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              <div className="md:col-span-2">
                <h3 className="mb-4 font-medium text-sm">Product Status</h3>
                <div className="gap-4 grid md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="isPublished"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div>
                          <FormLabel>Published</FormLabel>
                          <FormDescription>
                            This product will be visible on the store
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="featured"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div>
                          <FormLabel>Featured</FormLabel>
                          <FormDescription>
                            Show this product on the homepage
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isNew"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div>
                          <FormLabel>New</FormLabel>
                          <FormDescription>
                            Mark this product as new
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Sale Settings */}
              <div className="md:col-span-2">
                <h3 className="mb-4 font-medium text-sm">Sale Settings</h3>
                <FormField
                  control={form.control}
                  name="sale.active"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2 mb-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value || false}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Enable Sale</FormLabel>
                    </FormItem>
                  )}
                />
              </div>
              {form.watch("sale.active") && (
                <Card className="md:col-span-2 p-4">
                  <CardContent className="gap-6 grid md:grid-cols-2 p-0">
                    <FormField
                      control={form.control}
                      name="sale.price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sale Price ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                const newPrice = Number.parseFloat(
                                  e.target.value
                                );
                                const originalPrice =
                                  form.getValues("regularPrice");
                                if (
                                  originalPrice > 0 &&
                                  newPrice > 0 &&
                                  newPrice < originalPrice
                                ) {
                                  const discount =
                                    ((originalPrice - newPrice) /
                                      originalPrice) *
                                    100;
                                  form.setValue(
                                    "sale.discountPercentage",
                                    Number.parseFloat(discount.toFixed(2))
                                  );
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="sale.discountPercentage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Discount Percentage (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => {
                                const newDiscount = Number.parseFloat(
                                  e.target.value
                                );
                                const originalPrice =
                                  form.getValues("regularPrice");
                                if (
                                  originalPrice > 0 &&
                                  newDiscount > 0 &&
                                  newDiscount < 100
                                ) {
                                  const price =
                                    originalPrice * ((100 - newDiscount) / 100);
                                  form.setValue(
                                    "sale.price",
                                    Number.parseFloat(price.toFixed(2))
                                  );
                                }

                                field.onChange(e);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="sale.originalPrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Original Price ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              disabled
                              {...field}
                              value={form.getValues("regularPrice")}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="sale.startDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sale Start Date</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              value={
                                field.value
                                  ? new Date(field.value)
                                      .toISOString()
                                      .split("T")[0]
                                  : ""
                              }
                              onChange={(e) =>
                                field.onChange(
                                  new Date(e.target.value).getTime()
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="sale.endDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sale End Date (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              value={
                                field.value
                                  ? new Date(field.value)
                                      .toISOString()
                                      .split("T")[0]
                                  : ""
                              }
                              onChange={(e) =>
                                field.onChange(
                                  e.target.value
                                    ? new Date(e.target.value).getTime()
                                    : null
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}

              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product slug</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Product slug"
                          {...field}
                          onChange={(e) =>
                            form.setValue("slug", slugify(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/dashboard/products")}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Saving..."
                : productId
                ? "Update Product"
                : "Create Product"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

const ProductImageCard = ({ image }: { image: Doc<"images"> }) => {
  const [isLoading, setIsLoading] = useState(false);
  const removeProductImage = useMutation(api.images.remove);

  const handleDelete = async () => {
    setIsLoading(true);
    const { success, error } = await tryCatch(
      removeProductImage({
        id: image._id
      })
    );

    if (success) {
      toast.success("Image removed successfully");
    } else {
      toast.error("Failed to remove image", {
        description: error?.message || "Please try again"
      });
    }

    setIsLoading(false);
  };

  return (
    <div className="group relative">
      <div className="border rounded-md aspect-square overflow-hidden">
        <img
          src={image.imageUrl}
          alt="Product image"
          className="w-full h-full object-cover"
        />
      </div>
      <div className="top-2 right-2 absolute opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          type="button"
          variant="destructive"
          size="icon"
          onClick={handleDelete}
          disabled={isLoading}
          className="w-8 h-8"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

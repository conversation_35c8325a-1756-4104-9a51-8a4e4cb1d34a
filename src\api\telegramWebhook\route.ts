import type { NextRequest } from "next/server";

import { NextResponse } from "next/server";

import { telegramClient } from "@/lib/bot/telegram-client";

export async function POST(req: NextRequest) {
  const body = await req.json();
  try {
    if (!body || !body.message) {
      console.warn("No body or message");
      return NextResponse.json({
        message: "No body or message",
        statusCode: 200,
      });
    }

    const message = body.message;
    const text = `
*🗣📢 NEW NOTIFICATION - USER INTERACTION WITH BOT*
⋆˚࿔ First Name: ${message.from.first_name} 
⟡ ݁₊ Last Name: ${message.from.last_name}
🙍🏻‍♂️ Username: ${message.from.username.replace(/_/g, "\\_")}
[🪪 View Profile](tg://user?id=${message.from.id}) 
💬 Message directly - http://t.me/${message.from.username.replace(/_/g, "\\_")}

⤵️ Message 
  ${message.text}

_🛡️ admin only_
    `;

    await telegramClient.sendToAdmin(text, "Markdown");

    return NextResponse.json({
      message: "Message Forwarded",
      statusCode: 200,
    });
  }
  catch (error) {
    console.error(error);
    return NextResponse.json({
      message: "Error sending message",
      statusCode: 400,
    });
  }
}

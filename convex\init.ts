import { internalMutation } from "./functions";
import { getPermission, PERMISSIONS, ROLES } from "./permissions";

const init = internalMutation({
  args: {},
  handler: async (ctx) => {
    if ((await ctx.table("roles").first()) !== null) {
      console.error("There's an existing roles setup already.");
      return;
    }

    const allPermissions = Object.values(PERMISSIONS).flatMap(group => Object.values(group));
    await ctx.table("permissions").insertMany(allPermissions.map(name => ({ name })));

    await ctx.table("roles").insert({
      name: ROLES.ADMIN,
      isDefault: false,
      permissions: await Promise.all(allPermissions.map(perm => getPermission(ctx, perm))),
    });

    await ctx.table("roles").insert({
      name: ROLES.EDITOR,
      isDefault: false,
      permissions: await Promise.all([
        ...Object.values(PERMISSIONS).map(group => getPermission(ctx, group.read)),
        ...Object.values(PERMISSIONS).filter(group => group !== PERMISSIONS.users)
          .flatMap(group => [getPermission(ctx, group.update), getPermission(ctx, group.delete)])
      ]),
    });

    await ctx.table("roles").insert({
      name: ROLES.VIEWER,
      isDefault: false,
      permissions: await Promise.all(
        Object.values(PERMISSIONS).map(group => getPermission(ctx, group.read))
      ),
    });

    await ctx.table("roles").insert({
      name: ROLES.ANONYMOUS,
      isDefault: true,
      permissions: [],
    });
  },
});

export default init;
"use client";

import { useQuery } from "convex/react";
import { Menu, Search, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Container } from "@/components/ds";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Typography } from "@/components/ui/typography";
import { api } from "@/convex/_generated/api";

import { CartIcon } from "./cart-icon";
import { FavoriteIcon } from "./favoirite-icon";
import Logo from "./logo";
import { SearchPopover } from "./search-popover";

type NavItem = {
  label: string;
  href: string;
};
export const Navbar = () => {
  const categories = useQuery(api.categories.list, { isActive: true });
  const [isOpen, setIsOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  if (!categories) {
    return <div>...</div>;
  }

  const navItems: NavItem[] = [
    { label: "Home", href: "/" },
    ...categories.map((category) => ({
      label: category.name,
      href: `/${category.slug}`
    })),
    { label: "Contact", href: "/contact" }
  ];

  return (
    <header className="bg-white border-gray-200 border-b">
      <Container>
        {/* md  to xxxl */}
        <div className="hidden md:flex justify-between items-center gap-4">
          {/* Logo */}
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-4 xl:gap-8">
            {navItems.map((item) => (
              <Typography
                asChild
                key={item.href}
                variant="smallText"
                className="font-medium hover:text-primary text-xs uppercase"
              >
                <Link href={item.href}>{item.label}</Link>
              </Typography>
            ))}
          </nav>

          {/* Icons */}
          <div className="flex items-center gap-3 md:gap-4">
            <SearchPopover />
            <FavoriteIcon />
            <CartIcon />

            {/* Mobile menu */}
            <Button
              variant="link"
              size="icon"
              onClick={() => setIsOpen(true)}
              className="hidden lg:hidden md:block hover:text-primary cursor-pointer"
            >
              <Menu className="size-6" />
            </Button>
          </div>
        </div>

        {/* Mobile to md */}
        <div className="md:hidden relative flex justify-between items-center gap-4">
          {/* Mobile menu */}
          <Button
            variant="link"
            size="icon"
            onClick={() => setIsOpen(true)}
            className="lg:hidden hover:text-primary cursor-pointer"
          >
            <Menu className="size-6" />
          </Button>

          {/* Logo */}
          <Logo />

          {/* Icons */}
          <div className="flex items-center gap-2 md:gap-4">
            {showSearch && (
              <SearchPopover className="top-[calc(100%+16px)] left-1/2 absolute bg-background w-72 -translate-x-1/2" />
            )}

            <Button
              variant="link"
              size="icon"
              aria-label="Search for products"
              onClick={() => setShowSearch(!showSearch)}
            >
              {showSearch ? (
                <X className="size-6" />
              ) : (
                <Search className="size-6" />
              )}
            </Button>
            <FavoriteIcon />
            <CartIcon />
          </div>
        </div>
        <MobileMenu navItems={navItems} isOpen={isOpen} setIsOpen={setIsOpen} />
      </Container>
    </header>
  );
};

type MobileMenuProps = {
  navItems: NavItem[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
};

function MobileMenu({ navItems, isOpen, setIsOpen }: MobileMenuProps) {
  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetContent side="left" className="lg:hidden w-[300px]">
        <div className="flex flex-col gap-4 h-full">
          <div className="bg-muted/50 p-4">
            <Logo />
          </div>
          <nav className="flex-1 overflow-y-auto">
            {navItems.map((item) => (
              <Typography
                key={item.href}
                asChild
                className="block px-4 py-3 border-muted border-b font-medium hover:text-primary text-sm uppercase"
              >
                <Link href={item.href}>{item.label}</Link>
              </Typography>
            ))}
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
}

"use client";

import { Arrow<PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { CategoryForm } from "../_components/category-form";

export default function NewCategoryPage() {
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  return (
    <div className="flex flex-col gap-4 p-4">
      <PageSubHeader
        title="Add New Category"
        description="Create a new product category"
        breadcrumbs={[
          { title: "Dashboard", href: "/dashboard" },
          { title: "Categories", href: "/dashboard/categories" },
          { title: "New Category", current: true }
        ]}
      >
        <Button variant="outline" asChild>
          <Link href="/dashboard/categories">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Categories
          </Link>
        </Button>
      </PageSubHeader>

      <RoleGate
        allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]}
        redirectTo="/dashboard/categories"
      >
        <CategoryForm />
      </RoleGate>
    </div>
  );
}

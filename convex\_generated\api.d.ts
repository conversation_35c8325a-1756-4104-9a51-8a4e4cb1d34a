/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as brands from "../brands.js";
import type * as categories from "../categories.js";
import type * as functions from "../functions.js";
import type * as http from "../http.js";
import type * as images from "../images.js";
import type * as init from "../init.js";
import type * as permissions from "../permissions.js";
import type * as products from "../products.js";
import type * as scents from "../scents.js";
import type * as types from "../types.js";
import type * as users from "../users.js";
import type * as utils_index from "../utils/index.js";
import type * as utils_validators from "../utils/validators.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  brands: typeof brands;
  categories: typeof categories;
  functions: typeof functions;
  http: typeof http;
  images: typeof images;
  init: typeof init;
  permissions: typeof permissions;
  products: typeof products;
  scents: typeof scents;
  types: typeof types;
  users: typeof users;
  "utils/index": typeof utils_index;
  "utils/validators": typeof utils_validators;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;

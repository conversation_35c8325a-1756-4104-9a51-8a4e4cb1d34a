"use client";

import { Eye, Heart, ShoppingCart } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

import type { Product } from "@/types";

import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { useCart } from "@/hooks/use-cart";
import { useWishlist } from "@/hooks/use-wishlist";
import { convertToLocale } from "@/utils/money";

import { ProductQuickView } from "./product-quick-view";

type ProductCardProps = {
  product: Product;
};

export function ProductCard({ product }: ProductCardProps) {
  const { addItem } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();

  const toggleWishlist = () => {
    if (isInWishlist(product._id)) {
      removeFromWishlist(product._id);
      toast.success("Removed from wishlist");
    } else {
      addToWishlist(product);
      toast.success("Added to wishlist");
    }
  };

  const handleAddToCart = () => {
    addItem(product, 1);
    toast.success("Added to cart");
  };

  return (
    <>
      <div className="group/product relative">
        {/* Sold out badge */}
        {product.stock === 0 ? (
          <Typography
            variant="span"
            className="top-3 left-3 z-10 absolute bg-foreground px-2 py-1 rounded-full text-primary-foreground text-xs"
          >
            Sold Out
          </Typography>
        ) : (
          // {/* Sale badge */}
          product.sale?.active && (
            <Typography
              variant="span"
              className="top-3 left-3 z-10 absolute bg-primary px-2 py-1 rounded-full text-primary-foreground text-xs"
            >
              Save {product.sale.discountPercentage}%
            </Typography>
          )
        )}

        {/* Product image */}
        <div className="group/image relative bg-muted-foreground rounded-xl overflow-hidden">
          <Link href={`/products/${product.slug}`}>
            <div className="relative w-full aspect-[19/21]">
              {product.images.length > 1 ? (
                <>
                  <img
                    src={product.images[1].imageUrl}
                    alt={product.name}
                    className="absolute inset-0 size-full object-cover"
                  />
                  <img
                    src={product.images[0].imageUrl}
                    alt={product.name}
                    className="absolute inset-0 group-hover/image:opacity-0 size-full object-cover transition-opacity duration-300 ease-in-out"
                  />
                </>
              ) : (
                <img
                  src={product.images[0].imageUrl}
                  alt={product.name}
                  className="absolute inset-0 size-full object-cover"
                />
              )}
            </div>
          </Link>

          {/* Action buttons overlay */}
          <div className="top-3 right-3 z-10 absolute flex flex-col space-y-2 opacity-0 group-hover/product:opacity-100 transition-opacity duration-300">
            <Button
              variant="secondary"
              size="icon"
              onClick={toggleWishlist}
              className={`rounded-full flex items-center justify-center shadow-sm hover:bg-primary hover:text-primary-foreground ${
                isInWishlist(product._id) &&
                "bg-red-500 text-primary-foreground"
              }`}
              aria-label="Add to wishlist"
            >
              <Heart size={16} />
            </Button>
            <ProductQuickView
              product={product}
              trigger={
                <Button
                  variant="secondary"
                  size="icon"
                  className="flex justify-center items-center hover:bg-primary shadow-sm rounded-full hover:text-primary-foreground"
                  aria-label="Quick view"
                >
                  <Eye size={16} />
                </Button>
              }
            />

            <Button
              variant="secondary"
              size="icon"
              onClick={handleAddToCart}
              disabled={product.stock === 0}
              className={`rounded-full flex items-center justify-center shadow-sm hover:bg-primary hover:text-primary-foreground  ${
                product.stock === 0 ? "cursor-not-allowed opacity-50" : ""
              }`}
              aria-label="Add to cart"
            >
              <ShoppingCart size={16} />
            </Button>
          </div>
        </div>

        {/* Product info */}
        <div className="flex flex-col items-center gap-2 p-4">
          <Typography
            asChild
            variant="h3"
            weight="semibold"
            className="font-playfair hover:text-primary text-lg text-center line-clamp-2 leading-[1.25] transition-colors duration-300"
          >
            <Link href={`/products/${product.slug}`}>{product.name}</Link>
          </Typography>
          <div className="font-medium">
            {product.sale?.active ? (
              <div className="flex items-center gap-2">
                <Typography
                  variant="body"
                  weight="bold"
                  className="text-primary text-center"
                >
                  {convertToLocale({ amount: product.sale.price })}
                </Typography>
                <Typography
                  variant="mutedText"
                  weight="bold"
                  className="text-center line-through"
                >
                  {convertToLocale({ amount: product.sale.originalPrice })}
                </Typography>
              </div>
            ) : (
              <Typography
                variant="body"
                weight="bold"
                className="text-primary text-center"
              >
                {convertToLocale({ amount: product.price })}
              </Typography>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

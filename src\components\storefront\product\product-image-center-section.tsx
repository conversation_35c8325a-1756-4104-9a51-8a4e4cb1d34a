import type { Dispatch, SetStateAction } from "react";

import Image from "next/image";

import type { CarouselApi } from "@/components/ui/carousel";
import type { Image as ImageType } from "@/types";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import { cn } from "@/utils";

type ProductImageCenterSectionProps = {
  images: ImageType[];
  setApi: Dispatch<SetStateAction<CarouselApi>>;
  className?: string;
};

export function ProductImageCenterSection({
  className,
  images,
  setApi
}: ProductImageCenterSectionProps) {
  const hasOnlyOneImage = images.length <= 1;
  return (
    <div className={cn("flex flex-col", className)}>
      <div className="md:top-[100px] md:sticky">
        <Carousel setApi={setApi}>
          <CarouselContent>
            {images.map((image, index) => (
              <CarouselItem className="relative aspect-square" key={image._id}>
                <Image
                  alt="Product image"
                  src={image.imageUrl}
                  fill
                  priority={index === 0}
                  sizes="(max-width: 450px) 300px, 480px"
                  className="object-cover"
                />
              </CarouselItem>
            ))}
          </CarouselContent>
          {!hasOnlyOneImage && (
            <>
              <CarouselPrevious
                variant="default"
                className="bottom-[50%] left-1 bg-foreground hover:bg-foreground/80 md:size-8"
              />
              <CarouselNext
                variant="default"
                className="right-1 bottom-[50%] bg-foreground hover:bg-foreground/80 md:size-8"
              />
            </>
          )}
        </Carousel>
      </div>
    </div>
  );
}

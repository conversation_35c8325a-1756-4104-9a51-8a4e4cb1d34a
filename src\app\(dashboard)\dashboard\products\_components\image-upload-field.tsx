"use client";

import type React from "react";

import { ImageP<PERSON>, Trash2, Upload } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { cn } from "@/utils";

type ImageUploadFieldProps = {
  images: string[];
  onChange: (images: string[]) => void;
};

export function ImageUploadField({
  images = [],
  onChange
}: ImageUploadFieldProps) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    // In a real app, you would handle file uploads here
    // For this demo, we'll just add a placeholder image
    if (images.length < 5) {
      onChange([...images, "/placeholder.svg?height=200&width=200"]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // In a real app, you would handle file uploads here
    // For this demo, we'll just add a placeholder image
    if (images.length < 5 && e.target.files?.length) {
      onChange([...images, "/placeholder.svg?height=200&width=200"]);
    }
  };

  const handleRemoveImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    onChange(newImages);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2">
        <h3 className="text-sm font-medium">Product Images</h3>
        <p className="text-sm text-muted-foreground">
          Upload up to 5 images for this product. The first image will be used
          as the product thumbnail.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <div
          className={cn(
            "flex min-h-[200px] cursor-pointer flex-col items-center justify-center rounded-md border border-dashed p-4 transition-colors",
            isDragging
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/25"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => document.getElementById("image-upload")?.click()}
        >
          <div className="flex flex-col items-center gap-2 text-center">
            <Upload className="h-10 w-10 text-muted-foreground" />
            <h3 className="text-sm font-medium">
              Drag & drop or click to upload
            </h3>
            <p className="text-xs text-muted-foreground">
              Supported formats: JPEG, PNG, WebP
            </p>
            <input
              type="file"
              id="image-upload"
              className="hidden"
              accept="image/*"
              onChange={handleFileChange}
              multiple
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-sm font-medium">Current Images</h3>
          {images.length === 0 ? (
            <div className="flex h-[150px] items-center justify-center rounded-md border border-dashed">
              <p className="text-sm text-muted-foreground">
                No images uploaded yet
              </p>
            </div>
          ) : (
            <div className="grid gap-4 sm:grid-cols-2">
              {images.map((image, index) => (
                <div
                  key={index}
                  className="group relative aspect-square rounded-md border"
                >
                  <img
                    src={image || "/placeholder.svg"}
                    alt={`Product image ${index + 1}`}
                    className="h-full w-full rounded-md object-cover"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveImage(index);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  {index === 0 && (
                    <div className="absolute bottom-2 left-2 rounded-md bg-primary px-2 py-1 text-xs text-primary-foreground">
                      Thumbnail
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {images.length < 5 && (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => document.getElementById("image-upload")?.click()}
            >
              <ImagePlus className="mr-2 h-4 w-4" />
              Add Image
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

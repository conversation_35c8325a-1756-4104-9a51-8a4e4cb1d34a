"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation } from "convex/react";
import { Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import type { Id } from "@/convex/_generated/dataModel";

import { uploadImage } from "@/actions/image.actions";
import SingleFileUpload from "@/components/single-file-upload";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { api } from "@/convex/_generated/api";
import { slugify } from "@/utils";
import { tryCatch } from "@/utils/try-catch";

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Category name must be at least 2 characters."
  }),
  slug: z.string().min(2, {
    message: "Category slug must be at least 2 characters."
  }),

  description: z.string().optional(),

  isActive: z.boolean(),

  imageFile: z.any().optional()
});

type FormValuesType = z.infer<typeof formSchema>;

type CategoryFormProps = {
  initialData?: Partial<FormValuesType> & {
    imageUrl?: string;
    imageId?: string;
  };
  categoryId?: string;
};

export function CategoryForm({ initialData, categoryId }: CategoryFormProps) {
  const router = useRouter();

  const [activeTab, setActiveTab] = useState<"general" | "media">("general");
  const createCategory = useMutation(api.categories.create);
  const updateCategory = useMutation(api.categories.update);

  const defaultValues: Partial<FormValuesType> = {
    name: "",
    description: "",
    isActive: true,
    ...initialData
  };

  const form = useForm<FormValuesType>({
    resolver: zodResolver(formSchema),
    defaultValues
  });
  const isSubmitting = form.formState.isSubmitting;

  const onSubmit = async (values: FormValuesType) => {
    let imageUrl, imageId;

    if (values.imageFile) {
      const { data, success, message } = await uploadImage(values.imageFile);

      if (!success || !data) {
        toast.error(message);
      } else {
        imageUrl = data.imageUrl;
        imageId = data.storageId;
        toast.success(message);
      }
    }

    if (!categoryId) {
      const { success, error } = await tryCatch(
        createCategory({
          name: values.name,
          slug: values.slug,
          description: values.description,
          isActive: values.isActive,
          imageUrl,
          imageId: imageId as Id<"_storage"> | undefined
        })
      );
      if (!success) {
        console.error(error);
        toast.error("Error creating category. Please try again.", {
          description: error.message
        });
        return;
      }
      toast.success("Category created");
      router.push("/dashboard/categories");
      return;
    }

    const { success, error } = await tryCatch(
      updateCategory({
        id: categoryId as Id<"categories">,
        name: values.name,
        slug: values.slug,
        description: values.description,
        isActive: values.isActive,
        imageUrl,
        imageId: imageId as Id<"_storage"> | undefined
      })
    );
    if (!success) {
      console.error(error);
      toast.error("Error updating category. Please try again.", {
        description: error.message
      });

      return;
    }
    toast.success("Category updated");
    router.push("/dashboard/categories");
  };

  const onErrors = (errors: any) => {
    const formErrors = Object.values(errors).map((error: any) => error.message);
    toast.error(formErrors.join("\n"));
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit, onErrors)}
        className="space-y-6"
      >
        <Card className="p-0">
          <Tabs
            defaultValue="general"
            value={activeTab}
            onValueChange={(value) =>
              setActiveTab(value as "general" | "media")
            }
          >
            <TabsList className="grid grid-cols-2 bg-transparent p-0 border-b rounded-none w-full">
              <TabsTrigger
                value="general"
                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary border-transparent border-b-2 rounded-none"
              >
                General
              </TabsTrigger>
              <TabsTrigger
                value="media"
                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary border-transparent border-b-2 rounded-none"
              >
                Media
              </TabsTrigger>
            </TabsList>
            <TabsContent value="general" className="pt-4">
              <CardContent className="space-y-6">
                <div className="gap-6 grid md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Category name"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              form.setValue("slug", slugify(e.target.value));
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Active</FormLabel>
                          <FormDescription>
                            This category will be visible on the store
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                  <div className="space-y-4 md:col-span-2">
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              rows={5}
                              placeholder="Category description"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="slug"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category slug</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Category slug"
                              {...field}
                              onChange={(e) =>
                                form.setValue("slug", slugify(e.target.value))
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </TabsContent>

            <TabsContent value="media" className="pt-4">
              <CardContent className="space-y-6">
                {initialData?.imageUrl && initialData?.imageId ? (
                  <CategoryImageCard
                    imageUrl={initialData.imageUrl}
                    imageId={initialData.imageId}
                  />
                ) : (
                  <FormField
                    control={form.control}
                    name="imageFile"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Image</FormLabel>
                        <FormControl>
                          <div className="flex items-center gap-4">
                            <SingleFileUpload
                              onFilesAdded={(files) => {
                                const length = files.length;
                                if (length > 0) {
                                  field.onChange(files[length - 1].file);
                                }
                              }}
                            />
                          </div>
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </TabsContent>
          </Tabs>

          <CardFooter className="flex justify-between p-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/dashboard/categories")}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <div className="flex gap-2">
              {activeTab !== "general" && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setActiveTab("general")}
                >
                  Previous
                </Button>
              )}
              {activeTab !== "media" && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setActiveTab("media")}
                >
                  Next
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : categoryId
                  ? "Update Category"
                  : "Create Category"}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

const CategoryImageCard = ({
  imageUrl,
  imageId
}: {
  imageUrl: string;
  imageId: string;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const deleteImage = useMutation(api.categories.removeImage);

  const handleDelete = async () => {
    setIsLoading(true);
    const { success, error } = await tryCatch(
      deleteImage({ storageId: imageId as Id<"_storage"> })
    );
    if (success) {
      toast.success("Image deleted successfully");
    } else {
      toast.error(error?.message || "Failed to delete Image");
    }
    setIsLoading(false);
  };

  return (
    <div className="relative w-fit">
      <img src={imageUrl} alt="Image" className="rounded-sm object-contain" />

      <div className="top-0 right-0 absolute">
        <Button
          type="button"
          variant="destructive"
          size="icon"
          onClick={handleDelete}
          disabled={isLoading}
        >
          <Trash2 />
        </Button>
      </div>
    </div>
  );
};

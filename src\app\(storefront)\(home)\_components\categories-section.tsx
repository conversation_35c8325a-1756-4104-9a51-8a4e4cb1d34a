"use client";

import { useQuery } from "convex/react";

import { Container, Section } from "@/components/ds";
import Loading from "@/components/storefront/loading";
import { api } from "@/convex/_generated/api";

import CategoryCard from "./category-card";

export const CategoriesSection = () => {
  const categories = useQuery(api.categories.list, {
    isActive: true
  });

  if (!categories) return <Loading />;

  console.log("CategoriesSection:categories", categories);

  return (
    <Section>
      <Container>
        <div className="gap-8 sm:gap-8 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {categories.map((category) => (
            <CategoryCard
              key={category._id}
              title={category.name}
              image={category.imageUrl}
              slug={category.slug}
            />
          ))}
        </div>
      </Container>
    </Section>
  );
};

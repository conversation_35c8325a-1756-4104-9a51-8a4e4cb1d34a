import { v } from "convex/values";

import { mutation, query } from "./functions";
import { PERMISSIONS, viewerHasPermissionX } from "./permissions";
import { slugify } from "./utils";

export const get = query({
  args: {
    id: v.id("scents"),
  },
  handler: async (ctx, args) => {
    const scent = await ctx.table("scents").getX(args.id);

    return scent
  },
});

export const list = query({
  args: {
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.table("scents");
    if (args.isActive) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    return await query;
  },
});

export const metadata = query({
  handler: async (ctx) => {
    const scents = await ctx.table("scents");

    return {
      count: scents.length,
      active: scents.filter((scent) => scent.isActive).length,
    };
  }
})

export const create = mutation({
  args: {
    name: v.string(),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    imageUrl: v.optional(v.string()),
    imageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.scents.update);
    const scent = await ctx.table("scents").insert({
      ...args,
      slug: args.slug ?? slugify(args.name),
      isActive: args.isActive ?? true,
    }).get();

    return scent
  },
});

export const update = mutation({
  args: {
    id: v.id("scents"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    imageId: v.optional(v.id("_storage")),
    slug: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.scents.update);
    const { id, ...data } = args;
    const updatedScent = await ctx.table("scents").getX(id).patch(data).get();

    return updatedScent
  },
});

export const remove = mutation({
  args: {
    id: v.id("scents"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.scents.delete);
    return await ctx.table("scents").getX(args.id).delete();
  },
});

export const removeImage = mutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.scents.update);

    await ctx.storage.delete(args.storageId);
    await ctx.table("scents").getX("imageId", args.storageId).patch({ imageUrl: undefined, imageId: undefined });

  }
})
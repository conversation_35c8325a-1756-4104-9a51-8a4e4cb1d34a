import { v } from "convex/values";

import { mutation, query } from "./functions";
import { PERMISSIONS, viewerHasPermissionX } from "./permissions";
import { slugify } from "./utils";

export const get = query({
  args: {
    id: v.id("categories"),
  },
  handler: async (ctx, args) => {
    const category = await ctx.table("categories").getX(args.id);

    return category;
  },
});

export const getBySlug = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    const category = await ctx.table("categories").getX("slug", args.slug);

    return category;
  },
});

export const list = query({
  args: {
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.table("categories");
    if (args.isActive) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    return await query;
  },
});

export const metadata = query({
  handler: async (ctx) => {
    const categories = await ctx.table("categories");

    return {
      count: categories.length,
      active: categories.filter((category) => category.isActive).length,
    };
  }
})

export const create = mutation({
  args: {
    name: v.string(),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    imageUrl: v.optional(v.string()),
    imageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.categories.update);
    const category = await ctx.table("categories").insert({
      ...args,
      slug: args.slug ?? slugify(args.name),
      isActive: args.isActive ?? true,
    }).get();

    return category;
  },
});

export const update = mutation({
  args: {
    id: v.id("categories"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    imageId: v.optional(v.id("_storage")),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.categories.update);
    const { id, ...data } = args;
    const updatedCategory = await ctx.table("categories").getX(id).patch(data).get();

    return updatedCategory;
  },
});

export const remove = mutation({
  args: {
    id: v.id("categories"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.categories.delete);
    return await ctx.table("categories").getX(args.id).delete();
  },
});

export const removeImage = mutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.categories.update);

    await ctx.storage.delete(args.storageId);
    await ctx.table("categories").getX("imageId", args.storageId).patch({ imageUrl: undefined, imageId: undefined });

  }
})
"use client";

import Autoplay from "embla-carousel-autoplay";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

import type { CarouselApi } from "@/components/ui/carousel";

import { Section } from "@/components/ds";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import { Typography } from "@/components/ui/typography";

type HeroSlide = {
  id: number;
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  image: string;
  bgColor: string;
  positionRight?: boolean;
};

type HeroSectionProps = {
  slides: HeroSlide[];
};
export const HeroSection = ({ slides }: HeroSectionProps) => {
  const plugin = useRef(Autoplay({ delay: 6000 }));
  const [api, setApi] = useState<CarouselApi>();
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrentIndex(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrentIndex(api.selectedScrollSnap());

      api.on("slidesChanged", () => {
        setCurrentIndex(api.selectedScrollSnap());
      });
    });
  }, [api]);
  return (
    <Section className="left-[calc(-50vw+50%)] relative py-0 sm:py-0 w-[100vw]">
      <Carousel
        plugins={[plugin.current]}
        setApi={setApi}
        opts={{ skipSnaps: true, loop: true }}
        className="group/hero"
      >
        <CarouselContent className="mx-0 mt-0 border w-full min-h-[50vh] md:min-h-auto md:max-h-[65vh] md:aspect-[2/1]">
          {/* Main Carousel */}
          {slides.map((item) => (
            <CarouselItem
              key={item.id}
              className="relative flex justify-center items-center md:mx-4 w-full"
            >
              <div className="absolute inset-0">
                <Image
                  className="object-cover"
                  src={item.image}
                  alt="Featured"
                  fill
                  sizes="(max-width: 1280px) 100vw, 1280px"
                  style={{
                    marginBlock: "0px",
                    borderRadius: "0px",
                    borderWidth: "0px"
                  }}
                />
              </div>

              {/* Content */}
              <div className="relative mx-auto w-full max-w-7xl">
                <div
                  className={`flex flex-col gap-2 p-4 sm:p-10 lg:p-15 w-full md:w-1/2 max-w-3xl ${
                    item.positionRight && "ml-auto"
                  }`}
                >
                  <Typography
                    variant="h1"
                    weight="medium"
                    className="text-foreground"
                  >
                    {item.title}
                  </Typography>
                  <Typography variant="p" className="text-muted-foreground">
                    {item.description}
                  </Typography>

                  <div className="pt-4">
                    <Button size="lg" className="group" asChild>
                      <Link
                        href={`/${item.buttonLink ?? "sale"}`}
                        prefetch={false}
                        aria-label={item.buttonLink ?? "sale"}
                      >
                        {item.buttonText ?? "Explore Collection"}
                        <ArrowRight className="ml-2 size-4 transition-transform group-hover:translate-x-1 duration-300" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>

        {slides.length > 1 ? (
          <>
            {/* Navigation */}
            <div className="hidden group-hover/hero:block">
              <CarouselPrevious
                variant="default"
                size="icon"
                className="left-4 lg:left-12 bg-foreground backdrop-blur-sm size-10 transition-colors duration-300"
              />
              <CarouselNext
                variant="default"
                size="icon"
                className="right-4 lg:right-12 bg-foreground backdrop-blur-sm size-10 transition-colors duration-300"
              />
            </div>

            {/* Indicators */}
            <div className="right-1/2 bottom-6 absolute flex items-center gap-2 translate-x-1/2">
              {slides.map((_, index) => (
                <button
                  key={index}
                  aria-label={`Go to slide ${index + 1}`}
                  className={`h-2 w-2 border rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? "bg-primary border-primary w-4"
                      : "bg-primary-foreground/60 border-primary hover:bg-primary-foreground cursor-pointer"
                  }`}
                  onClick={() => api?.scrollTo(index)}
                />
              ))}
            </div>
          </>
        ) : null}
      </Carousel>
    </Section>
  );
};

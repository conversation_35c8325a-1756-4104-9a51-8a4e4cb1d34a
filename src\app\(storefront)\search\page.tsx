"use client";

import { useQuery } from "convex/react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

import { Breadcrumbs } from "@/components/dashboard/breadcrumbs";
import Loading from "@/components/storefront/loading";
import { ProductsGrid } from "@/components/storefront/product/products-grid";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { api } from "@/convex/_generated/api";

export default function SearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams?.get("query");

  const searchedProducts = useQuery(api.products.search, {
    query: query ?? ""
  });

  if (!searchedProducts) return <Loading />;

  const breadcrumbs = [
    { title: "Home", href: "/" },
    { title: "Search", current: true }
  ];

  return (
    <div className="space-y-8 py-8">
      <Breadcrumbs breadcrumbs={breadcrumbs} />

      <div className="mb-8">
        <Typography variant="h2">
          Search Results: <span className="font-medium">"{query}"</span>
        </Typography>
      </div>

      <div className="w-full">
        {searchedProducts?.products.length > 0 ? (
          <ProductsGrid products={searchedProducts?.products} />
        ) : (
          <div className="bg-card shadow-sm py-12 rounded-lg text-center">
            <Typography variant="h3" className="mb-4">
              No products found
            </Typography>
            <Typography variant="p" className="mb-6 text-muted-foreground">
              Try adjusting your search or filter to find what you're looking
              for.
            </Typography>
            <Button asChild>
              <Link href="/category">View All Products</Link>
            </Button>
          </div>
        )}

        {searchedProducts?.products.length > 0 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="icon">
                &laquo;
              </Button>
              <Button size="icon">1</Button>
              <Button variant="outline" size="icon">
                2
              </Button>
              <Button variant="outline" size="icon">
                &raquo;
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

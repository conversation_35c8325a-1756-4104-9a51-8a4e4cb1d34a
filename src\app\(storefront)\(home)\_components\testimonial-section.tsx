"use client";

import { Section } from "@/components/ds";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import { Typography } from "@/components/ui/typography";

const testimonials = [
  {
    id: 1,
    text: '"Arome supplements have been a game-changer for me! I feel more energized and healthier than ever. Highly recommend!"',
    author: "<PERSON>",
    status: "Verified Buyer"
  },
  {
    id: 2,
    text: '"Fast shipping and great customer support. The products are high-quality, with there were more flavor options, excellent service"',
    author: "<PERSON>",
    status: "Verified Buyer"
  },
  {
    id: 3,
    text: '"Excellent service and top-notch supplements. The loyalty rewards is a great bonus. Will definitely keep ordering!"',
    author: "<PERSON>",
    status: "Verified Buyer"
  }
];

export const TestimonialSection = () => {
  return (
    <Section className="space-y-8 my-12">
      <div className="flex flex-col items-center gap-4 mx-auto max-w-2xl">
        <Typography
          variant="h2"
          weight="semibold"
          className="font-playfair text-center"
        >
          Trusted by Thousands of Satisfied Customers.
        </Typography>
      </div>

      <Carousel opts={{ skipSnaps: true }} className="group/content relative">
        <CarouselContent>
          {testimonials.map((testimonial, idx) => (
            <CarouselItem
              className="mx-1 md:mx-2 sm:basis-1/2 lg:basis-1/3"
              key={`c_${testimonial.id}${idx}`}
            >
              <TestimonialCard key={testimonial.id} {...testimonial} />
            </CarouselItem>
          ))}
        </CarouselContent>

        <CarouselPrevious
          variant="default"
          aria-label="Previous testimonial"
          className="top-[120%] right-12 left-auto absolute transition-colors duration-500"
        />
        <CarouselNext
          variant="default"
          aria-label="Next testimonial"
          className="top-[120%] right-0 absolute transition-colors duration-500"
        />
      </Carousel>
    </Section>
  );
};

function TestimonialCard({
  text,
  author,
  status
}: {
  text: string;
  author: string;
  status: string;
}) {
  return (
    <Card>
      <CardContent className="flex flex-col gap-8">
        <Typography variant="p" className="italic">
          {text}
        </Typography>
        <div className="flex items-center">
          <div className="flex justify-center items-center bg-primary mr-4 rounded-full w-10 h-10 text-primary-foreground">
            {author.charAt(0)}
          </div>
          <div>
            <Typography weight="medium">{author}</Typography>
            <Typography variant="smallText" className="text-green-600">
              ✓ {status}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

import { defineEnt, defineEntSchema, getEntDefinitions } from "convex-ents";
import { v } from "convex/values";

import { vPermission, vRole } from "./permissions";

const users = defineEnt({
  firstName: v.string(),
  lastName: v.string(),
  fullName: v.string(),
  imageUrl: v.optional(v.string()),
  imageId: v.optional(v.id("_storage")),
})
  .field("email", v.string(), { unique: true })
  .field("externalId", v.string(), { unique: true })
  .edge("image", { to: "_storage", field: "imageId", deletion: "hard", optional: true })
  .edge("role");

const roles = defineEnt({
  isDefault: v.boolean(),
})
  .field("name", vRole, { unique: true })
  .edges("permissions")
  .edges("users", { ref: true });

const permissions = defineEnt({})
  .field("name", vPermission, { unique: true })
  .edges("roles");

const products = defineEnt({
  name: v.string(),
  shortDescription: v.string(),
  description: v.string(),
  regularPrice: v.number(),
  sale: v.optional(v.object({
    active: v.boolean(),
    price: v.number(),
    discountPercentage: v.number(),
    originalPrice: v.number(),
    startDate: v.number(),
    endDate: v.optional(v.number()),
  })),
  stock: v.number(),
  updatedAt: v.number(),
})
  .field("slug", v.string(), { unique: true })
  .field("featured", v.boolean(), { default: false })
  .field("isPublished", v.boolean(), { default: true })
  .field("isNew", v.boolean(), { default: false })
  .field("price", v.number(), { index: true })
  .searchIndex("search", { searchField: "name" })
  .edge("category", { to: "categories" })
  .edge("brand")
  .edge("scent", { field: "scentId", optional: true })
  .edges("images", { ref: true });

const images = defineEnt({
  imageUrl: v.string(),
  imageId: v.id("_storage"),
})
  .field("isPrimary", (v.boolean()), { default: false })
  .edge("image", { to: "_storage", field: "imageId", deletion: "hard" })
  .edge("product", { field: "productId" })

const categories = defineEnt({
  name: v.string(),
  description: v.optional(v.string()),
  imageUrl: v.optional(v.string()),
  imageId: v.optional(v.id("_storage")),
})
  .field("slug", v.string(), { unique: true })
  .field("isActive", v.boolean(), { default: true })
  .edge("image", { to: "_storage", field: "imageId", deletion: "hard", optional: true })
  .edges("products", { ref: true });

const brands = defineEnt({
  name: v.string(),
  description: v.optional(v.string()),
  logoUrl: v.optional(v.string()),
  logoId: v.optional(v.id("_storage")),
})
  .field("slug", v.string(), { unique: true })
  .field("isActive", v.boolean(), { default: true })
  .edge("image", { to: "_storage", field: "logoId", deletion: "hard", optional: true })
  .edges("products", { ref: true });

const scents = defineEnt({
  name: v.string(),
  description: v.optional(v.string()),
  imageUrl: v.optional(v.string()),
  imageId: v.optional(v.id("_storage")),
})
  .field("slug", v.string(), { unique: true })
  .field("isActive", v.boolean(), { default: true })
  .edge("image", { to: "_storage", field: "imageId", deletion: "hard", optional: true })
  .edges("products", { ref: true });

const schema = defineEntSchema({
  users,
  roles,
  permissions,
  products,
  images,
  categories,
  brands,
  scents,
});

export default schema;

export const entDefinitions = getEntDefinitions(schema);

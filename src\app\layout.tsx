import type { <PERSON>ada<PERSON> } from "next";

import "./globals.css";

import { Inter, Playfair_Display } from "next/font/google";
import { Toaster } from "sonner";

import { Layout } from "@/components/ds";
import { siteInfo } from "@/config/site-config";
import AppProvider from "@/provider";

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair-display",
  subsets: ["latin"]
});

const inter = Inter({
  subsets: ["latin"]
});

export const metadata: Metadata = {
  title: siteInfo.title,
  description: siteInfo.description
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Layout>
      <body
        className={`${playfairDisplay.variable} ${inter.className} antialiased`}
      >
        <AppProvider>{children}</AppProvider>
        <Toaster position="bottom-right" richColors />
      </body>
    </Layout>
  );
}

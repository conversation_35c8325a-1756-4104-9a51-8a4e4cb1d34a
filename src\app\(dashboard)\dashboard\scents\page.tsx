"use client";

import { useMutation, useQuery } from "convex/react";
import { Plus } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { DataTable } from "@/components/dashboard/data-table";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { tryCatch } from "@/utils/try-catch";

import { columns } from "./_components/columns";

export default function ScentsPage() {
  const scents = useQuery(api.scents.list, { isActive: undefined });
  const deleteScent = useMutation(api.scents.remove);
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (!scents) {
    return <Loading />;
  }

  const handleDeleteScent = async (id: string) => {
    const { success, error } = await tryCatch(
      deleteScent({ id: id as Id<"scents"> })
    );

    if (success) {
      toast.success("Scent deleted");
    } else {
      toast.error("Failed to delete scent", {
        description: error.message
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center justify-between">
        <PageSubHeader
          title="Scents"
          description="Manage product scent profiles"
          breadcrumbs={[
            { title: "Dashboard", href: "/dashboard" },
            { title: "Scents", current: true }
          ]}
        />

        <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
          <Link href="/dashboard/scents/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Scent
            </Button>
          </Link>
        </RoleGate>
      </div>

      <DataTable
        columns={columns(handleDeleteScent)}
        data={scents}
        searchKey="name"
        searchPlaceholder="Search scents..."
      />
    </div>
  );
}

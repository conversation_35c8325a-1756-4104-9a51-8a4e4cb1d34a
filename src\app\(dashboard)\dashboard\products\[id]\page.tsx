"use client";

import { useMutation, useQuery } from "convex/react";
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  Edit,
  Package,
  Tag,
  Trash2
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { formatDate } from "@/utils";
import { tryCatch } from "@/utils/try-catch";

import { DeleteProductDialog } from "../_components/delete-product-dialog";

export default function ProductViewPage() {
  const { id: productId } = useParams();
  const product = useQuery(api.products.get, {
    id: productId as Id<"products">
  });
  const deleteProduct = useMutation(api.products.remove);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  if (!product) {
    return <Loading />;
  }

  const handleDelete = async () => {
    const { success, error } = await tryCatch(
      deleteProduct({ id: product._id })
    );

    if (success) {
      toast.success("Product deleted");
      router.push("/dashboard/products");
    } else {
      toast.error("Failed to delete product", {
        description: error.message
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center justify-between">
        <PageSubHeader
          title={product.name}
          breadcrumbs={[
            { title: "Dashboard", href: "/dashboard" },
            { title: "Products", href: "/dashboard/products" },
            { title: product.name, current: true }
          ]}
        />

        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>

          <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
            <Button asChild>
              <Link href={`/dashboard/products/${product._id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Product
              </Link>
            </Button>
          </RoleGate>
          <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
            <Button
              variant="destructive"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </RoleGate>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardContent className="space-y-6">
            <div className="flex items-start justify-between">
              <div className="space-y-4 w-full">
                <div className="space-y-2">
                  <p>
                    <span className="font-medium">Slug:</span>
                    &nbsp;{product.slug}
                  </p>
                  <p>
                    <span className="font-medium">Short Description:</span>
                    &nbsp;{product.shortDescription}
                  </p>
                  <p>
                    <span className="font-medium">Full Description:</span>
                    &nbsp;{product.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Regular Price:</span>
                    <span className="ml-2">${product.regularPrice}</span>
                  </div>

                  {product.sale?.active && (
                    <div className="flex items-center">
                      <DollarSign className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Sale Price:</span>
                      <span className="ml-2">${product.sale.price}</span>
                    </div>
                  )}

                  <div className="flex items-center">
                    <Package className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Stock:</span>
                    <span className="ml-2">{product.stock} units</span>
                  </div>

                  <div className="flex items-center">
                    <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Category:</span>
                    <span className="ml-2">{product.category.name}</span>
                  </div>

                  <div className="flex items-center">
                    <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Brand:</span>
                    <span className="ml-2">{product.brand.name}</span>
                  </div>

                  {product.scent && (
                    <div className="flex items-center">
                      <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Scent:</span>
                      <span className="ml-2">{product.scent.name}</span>
                    </div>
                  )}
                </div>

                <div className="flex gap-4">
                  {product.featured && (
                    <Badge variant="destructive">Featured</Badge>
                  )}
                  {product.isNew && (
                    <Badge className="bg-blue-50 font-medium text-blue-500">
                      New
                    </Badge>
                  )}
                </div>
              </div>
              <Badge
                variant={product.isPublished ? "outline" : "secondary"}
                className={
                  product.isPublished
                    ? "bg-green-50 text-green-700"
                    : "bg-amber-50 text-amber-700"
                }
              >
                {product.isPublished ? "Published" : "Draft"}
              </Badge>
            </div>

            <Separator />

            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Updated:</span>
                <span className="ml-2">
                  {formatDate(new Date(product.updatedAt))}
                </span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Created:</span>
                <span className="ml-2">
                  {formatDate(new Date(product._creationTime))}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Product Media</CardTitle>
          </CardHeader>

          <CardContent className="space-y-4">
            {product.images && product.images.length > 0 ? (
              <div>
                <h4 className="mb-2 text-sm font-medium">Images</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {product.images.map((image, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-center rounded-sm border p-4"
                    >
                      <Image
                        src={image.imageUrl}
                        alt={`${product.name} image ${index + 1}`}
                        width={300}
                        height={300}
                        className="object-contain"
                      />
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p>No media</p>
            )}
          </CardContent>
        </Card>
      </div>

      <DeleteProductDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        productName={product.name}
        onConfirm={handleDelete}
      />
    </div>
  );
}

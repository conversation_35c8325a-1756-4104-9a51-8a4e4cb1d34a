"use client";

import { useQuery } from "convex/react";
import { useRouter } from "next/navigation";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { DataTable } from "@/components/dashboard/data-table";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { columns } from "./_components/columns";

export default function UsersPage() {
  const users = useQuery(api.users.list, { roleId: undefined });
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (!users) {
    return <Loading />;
  }

  return (
    <RoleGate allowedRoles={[ROLES.ADMIN]} redirectTo="/dashboard">
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center justify-between">
          <PageSubHeader
            title="Users"
            description="Manage user accounts and permissions"
            breadcrumbs={[
              { title: "Dashboard", href: "/dashboard" },
              { title: "Users", current: true }
            ]}
          />
        </div>

        <DataTable
          columns={columns}
          data={users}
          searchKey="name"
          searchPlaceholder="Search users..."
        />
      </div>
    </RoleGate>
  );
}

import type { Infer } from "convex/values";

import { v } from "convex/values";

import type { MutationCtx, QueryCtx } from "./types";

export const PERMISSIONS = {
  users: {
    read: "read:user",
    update: "edit:user",
    delete: "delete:user",
  },
  products: {
    read: "read:product",
    update: "edit:product",
    delete: "delete:product",
  },
  categories: {
    read: "read:category",
    update: "edit:category",
    delete: "delete:category",
  },
  brands: {
    read: "read:brand",
    update: "edit:brand",
    delete: "delete:brand",
  },
  scents: {
    read: "read:scent",
    update: "edit:scent",
    delete: "delete:scent",
  }
}

export const ROLES = {
  ADMIN: "Admin",
  EDITOR: "Editor",
  VIEWER: "Viewer",
  ANONYMOUS: "Anonymous",
};

export const vPermission = v.union(
  ...Object.values(PERMISSIONS).flatMap(group =>
    Object.values(group).map(permission => v.literal(permission))
  )
);
export const vRole = v.union(...Object.values(ROLES).map(role => v.literal(role)));

export type Permission = Infer<typeof vPermission>;
export type Role = Infer<typeof vRole>;

export async function getPermission(ctx: QueryCtx, name: Permission) {
  return (await ctx.table("permissions").getX("name", name))._id;
}

export async function getRolePermissions(ctx: QueryCtx, name: Role) {
  return (await getRole(ctx, name)).edge("permissions");
}

export async function getRole(ctx: QueryCtx, name: Role) {
  return await ctx.table("roles").getX("name", name);
}

export async function viewerWithPermission(
  ctx: QueryCtx,
  name: Permission,
) {
  const user = await ctx.table("users").getX(ctx.viewerX()._id);
  const permission = await getPermission(ctx, name);

  if (
    user === null
    || !(await user.edge("role").edge("permissions").has(permission))
  ) {
    return null;
  }
  return user;
}

export async function viewerHasPermission(
  ctx: QueryCtx,
  name: Permission,
) {
  const user = await viewerWithPermission(ctx, name);
  return user !== null;
}

export async function viewerWithPermissionX(
  ctx: MutationCtx,
  name: Permission,
) {
  const user = await viewerWithPermission(ctx, name);
  if (user === null) {
    throw new Error(`Viewer does not have the permission "${name}"`);
  }
  return user;
}

export async function viewerHasPermissionX(
  ctx: MutationCtx,
  name: Permission,
) {
  await viewerWithPermissionX(ctx, name);
  return true;
}

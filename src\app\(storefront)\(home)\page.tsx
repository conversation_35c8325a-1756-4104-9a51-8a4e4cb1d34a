import { fetchQuery } from "convex/nextjs";

import { ProductsSection } from "@/components/storefront/product/products-section";
import { api } from "@/convex/_generated/api";

import { CategoriesSection } from "./_components/categories-section";
import { HeroSection } from "./_components/hero-section";
import LimitedOfferSection from "./_components/limited-offer-section";
import { TestimonialSection } from "./_components/testimonial-section";

const heroSlides = [
  {
    id: 1,
    title: "Fragrance For Every Occasion",
    description:
      "Find the perfect scent that tells your unique story for any event, season, or mood.",
    buttonText: "SHOP NOW",
    buttonLink: "/category",
    image: "/placeholder.svg",
    bgColor: "bg-arome-softpink",
    positionRight: false
  },
  {
    id: 2,
    title: "Summer Scent Sale",
    description:
      "Dive into our Summer Scent Sale and enjoy up to 30% off on selected fragrances.",
    buttonText: "SHOP NOW",
    buttonLink: "/category?collection=sale",
    image: "/placeholder.svg",
    bgColor: "bg-purple-100",
    positionRight: true
  },
  {
    id: 3,
    title: "New Collection Arriving",
    description:
      "Discover our newest luxury fragrances for the modern individual.",
    buttonText: "DISCOVER",
    buttonLink: "/category?collection=new",
    image: "/placeholder.svg",
    bgColor: "bg-arome-cream",
    positionRight: false
  }
];

export default async function ProductPage() {
  const womenProducts = await fetchQuery(api.products.list, {
    isPublished: true
  });
  const saleProducts = await fetchQuery(api.products.list, {
    isPublished: true,
    onSale: true
  });

  return (
    <div className="space-y-24">
      {/* Hero Carousel Section */}
      <HeroSection slides={heroSlides} />

      {/* Categories Section */}
      <CategoriesSection />

      {/* Best Sellers for Women */}
      <ProductsSection
        title="Best Sellers For Women"
        description="Shop our most loved fragrances that captivate and enchant."
        products={womenProducts.products}
        carousel
      />

      {/* Sale Collection */}
      <ProductsSection
        title="Best Selling Fragrances"
        description="Explore our curated collection of popular scents for all occasions."
        products={saleProducts.products}
      />

      {/* Limited Time Offer */}
      <LimitedOfferSection />

      {/* Testimonials Section */}
      <TestimonialSection />
    </div>
  );
}

"use client";

import { useQuery } from "convex/react";
import { Package, ShoppingBag, Tag, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { LOW_STOCK_THRESHOLD } from "@/config/constants";
import { internalLinks, siteInfo } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

export default function DashboardPage() {
  const productsMetaData = useQuery(api.products.metadata);
  const categoriesMetaData = useQuery(api.categories.metadata);
  const brandsMetaData = useQuery(api.brands.metadata);
  const usersMetaData = useQuery(api.users.metadata);
  const lowStockProducts = useQuery(api.products.lowStock, {
    paginationOpts: undefined
  });
  const recentProducts = useQuery(api.products.list, {
    sortBy: "newest"
  });
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (
    !loaded ||
    !productsMetaData ||
    !categoriesMetaData ||
    !brandsMetaData ||
    !usersMetaData ||
    !lowStockProducts ||
    !recentProducts
  ) {
    return <Loading />;
  }

  return (
    <div className="flex flex-col">
      <PageSubHeader
        title="Dashboard Overview"
        description={`Welcome back to ${siteInfo.name} dashboard`}
        className="px-4 py-2"
      />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <RoleGate
          allowedRoles={[ROLES.ADMIN, ROLES.EDITOR, ROLES.VIEWER]}
          fallback={<div>Access denied</div>}
        >
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="gap-2">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Products
                </CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {productsMetaData.count}
                </div>
                <p className="text-sm text-muted-foreground">
                  {productsMetaData.published} published
                </p>
              </CardContent>
            </Card>
            <Card className="gap-2">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Categories
                </CardTitle>
                <Tag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categoriesMetaData.count}
                </div>
                <p className="text-sm text-muted-foreground">
                  {categoriesMetaData.active} active
                </p>
              </CardContent>
            </Card>
            <Card className="gap-2">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Brands
                </CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{brandsMetaData.count}</div>
                <p className="text-sm text-muted-foreground">
                  {brandsMetaData.active} active
                </p>
              </CardContent>
            </Card>
            <RoleGate allowedRoles={[ROLES.ADMIN]} fallback={null}>
              <Card className="gap-2">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Registered Users
                  </CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {usersMetaData.count}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {usersMetaData.admins} admins, |&nbsp;
                    {usersMetaData.editors} editors, |&nbsp;
                    {usersMetaData.viewers} viewers, |&nbsp;
                    {usersMetaData.anonymous} anonymous
                  </p>
                </CardContent>
              </Card>
            </RoleGate>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4 h-fit">
              <CardHeader>
                <CardTitle>Recent Products</CardTitle>
                <CardDescription>
                  Latest products added to your inventory
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {recentProducts.products.map((product) => (
                    <div
                      key={product._id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center gap-3">
                        <Link href={`/dashboard/products/${product._id}`}>
                          <Image
                            src={product.images[0].imageUrl}
                            alt={product.name}
                            width={80}
                            height={80}
                            className="object-cover aspect-square rounded-sm"
                          />
                        </Link>
                        <div>
                          <Link
                            href={`/dashboard/products/${product._id}`}
                            className="font-medium"
                          >
                            {product.name}
                          </Link>
                          <p className="text-sm text-muted-foreground">
                            {product.shortDescription}
                          </p>
                          <div>
                            <span className="text-sm text-muted-foreground font-medium mr-1">
                              {product.category.name},
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {product.brand.name}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2">
                        <span className="font-medium">${product.price}</span>
                        <span
                          className={`text-xs ${
                            product.isPublished
                              ? "text-green-500"
                              : "text-amber-500"
                          }`}
                        >
                          {product.isPublished ? "Published" : "Draft"}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            <Card className="col-span-3 h-fit">
              <CardHeader>
                <CardTitle>Low Stock Alert</CardTitle>
                <CardDescription>
                  Products that need to be restocked soon
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {lowStockProducts.products.map((product) => (
                    <div
                      key={product._id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center gap-3">
                        <Link href={`/dashboard/products/${product._id}`}>
                          <Image
                            src={product.images[0].imageUrl}
                            alt={product.name}
                            width={60}
                            height={60}
                            className="object-cover aspect-square rounded-sm"
                          />
                        </Link>
                        <div>
                          <Link
                            href={`/dashboard/products/${product._id}`}
                            className="font-medium"
                          >
                            {product.name}
                          </Link>
                          <p className="text-xs text-muted-foreground">
                            {product.brand.name}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span
                          className={`text-sm font-medium ${
                            product.stock < (2 / 3) * LOW_STOCK_THRESHOLD
                              ? "text-red-500"
                              : "text-amber-500"
                          }`}
                        >
                          {product.stock}
                          &nbsp; in stock
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </RoleGate>
      </div>
    </div>
  );
}

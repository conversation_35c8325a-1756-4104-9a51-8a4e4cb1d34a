"use client";

import { useQuery } from "convex/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { ProductForm } from "../../_components/product-form";

export default function EditProductPage() {
  const { id: productId } = useParams();
  const product = useQuery(api.products.get, {
    id: productId as Id<"products">
  });
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (!product) {
    return <Loading />;
  }

  return (
    <RoleGate
      allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]}
      redirectTo="/dashboard/products"
    >
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center justify-between">
          <PageSubHeader
            title={`Edit ${product.name}`}
            description="Update product information"
            breadcrumbs={[
              { title: "Dashboard", href: "/dashboard" },
              { title: "Products", href: "/dashboard/products" },
              {
                title: product.name,
                href: `/dashboard/products/${product._id}`
              },
              { title: "Edit", current: true }
            ]}
          />
          <Button variant="outline" asChild>
            <Link href={`/dashboard/products/${product._id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Product
            </Link>
          </Button>
        </div>

        <ProductForm initialData={product} productId={product._id} />
      </div>
    </RoleGate>
  );
}

import type { VariantProps } from "class-variance-authority";

import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";
import React from "react";

import { cn } from "@/utils";

const typographyVariants = cva("text-foreground", {
  variants: {
    variant: {
      h1: "text-[clamp(1.5rem,5vw,4.5rem)] leading-[1.2] tracking-tight font-playfair",
      h2: "text-[clamp(1.4rem,4vw,2.5rem)] leading-[1.25] font-playfair",
      h3: "text-[clamp(1.2rem,3vw,1.5rem)] leading-[1.3] font-playfair",
      h4: "text-[clamp(1.1rem,2.5vw,1.75rem)] leading-[1.35] font-playfair",
      h5: "text-[clamp(1rem,2vw,1.5rem)] leading-[1.4] font-playfair",
      h6: "text-[clamp(0.875rem,1.5vw,1.25rem)] leading-[1.5] font-playfair",

      body: "text-[clamp(1rem,2vw,1.25rem)] leading-[1.6]",
      p: "text-[clamp(0.875rem,1.5vw,1rem)] leading-[1.55]",

      price: "text-[clamp(1.2rem,3vw,1.75rem)] font-bold tracking-tight",
      button: "text-[clamp(1rem,2vw,1.25rem)] tracking-wide",
      span: "text-[clamp(0.875rem,1.5vw,1rem)] leading-[1.5]",

      blockquote:
        "text-[clamp(1rem,2vw,1.25rem)] leading-[1.7] italic border-l-4 border-gray-300 pl-4 my-4 text-gray-700",
      inlineCode:
        "text-[clamp(0.875rem,1.5vw,1rem)] font-mono bg-gray-100 rounded px-1.5 py-0.5",

      largeText: "text-[clamp(1.1rem,2.5vw,1.5rem)] leading-[1.5]",
      smallText: "text-[clamp(0.75rem,1.2vw,0.875rem)] leading-[1.4]",
      lead: "text-[clamp(1.1rem,2.2vw,1.5rem)] leading-[1.5] font-medium",
      mutedText: "text-[clamp(0.875rem,1.5vw,1rem)] leading-[1.5] text-gray-500"
    },
    weight: {
      bold: "font-bold",
      semibold: "font-semibold",
      normal: "font-normal",
      medium: "font-medium",
      light: "font-light"
    }
  },
  defaultVariants: {
    variant: "p",
    weight: "normal"
  }
});

type VariantPropType = VariantProps<typeof typographyVariants>;

const variantElementMap: Record<
  NonNullable<VariantPropType["variant"]>,
  string
> = {
  h1: "h1",
  h2: "h2",
  h3: "h3",
  h4: "h4",
  h5: "h5",
  h6: "h6",
  p: "p",
  body: "div",
  price: "span",
  button: "span",
  span: "span",
  blockquote: "blockquote",
  inlineCode: "code",
  largeText: "div",
  smallText: "small",
  lead: "p",
  mutedText: "p"
};

type TypographyProps = {
  asChild?: boolean;
  as?:
    | "h1"
    | "h2"
    | "h3"
    | "h4"
    | "h5"
    | "h6"
    | "p"
    | "span"
    | "blockquote"
    | "code"
    | "small"
    | "div";
} & React.HTMLAttributes<HTMLElement> &
  VariantPropType;

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  (
    { className, variant = "p", as, weight, asChild = false, ...props },
    ref
  ) => {
    const element = variant ? variantElementMap[variant] : "div";

    const Comp = asChild ? Slot : as || element;

    return (
      <Comp
        ref={ref}
        className={cn(
          typographyVariants({ variant, weight }),
          "m-0",
          className
        )}
        {...props}
      />
    );
  }
);

Typography.displayName = "Typography";

export { Typography, typographyVariants };

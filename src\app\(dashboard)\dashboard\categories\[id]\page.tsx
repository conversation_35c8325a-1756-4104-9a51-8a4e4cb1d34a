"use client";

import { useMutation, useQuery } from "convex/react";
import { ArrowLeft, Calendar, Edit, Trash2 } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { formatDate } from "@/utils";
import { tryCatch } from "@/utils/try-catch";

import { DeleteCategoryDialog } from "../_components/delete-category-dialog";

export default function CategoryViewPage() {
  const { id: categoryId } = useParams();
  const category = useQuery(api.categories.get, {
    id: categoryId as Id<"categories">
  });
  const deleteCategory = useMutation(api.categories.remove);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  if (!category) {
    return <Loading />;
  }

  const handleDeleteCategory = async () => {
    const { success, error } = await tryCatch(
      deleteCategory({ id: category._id })
    );

    if (success) {
      toast.success("Category deleted");
      router.push("/dashboard/categories");
    } else {
      toast.error("Failed to delete category", {
        description: error.message
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center justify-between">
        <PageSubHeader
          title={category.name}
          description={category.description}
          breadcrumbs={[
            { title: "Dashboard", href: "/dashboard" },
            { title: "Categories", href: "/dashboard/categories" },
            { title: category.name, current: true }
          ]}
        />

        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/categories">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Categories
            </Link>
          </Button>

          <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
            <Button asChild>
              <Link href={`/dashboard/categories/${category._id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Category
              </Link>
            </Button>
          </RoleGate>

          <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
            <Button
              variant="destructive"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </RoleGate>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Category Information</CardTitle>
            <CardDescription>
              Detailed information about this category
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-medium">
                  Category Name:&nbsp;{category.name}
                </h3>
                <p className="text-muted-foreground">
                  Description:&nbsp;{category.description}
                </p>
              </div>
              <Badge
                variant={category.isActive ? "outline" : "secondary"}
                className={
                  category.isActive
                    ? "bg-green-50 text-green-700"
                    : "bg-amber-50 text-amber-700"
                }
              >
                {category.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>

            <Separator />

            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Created:</span>
                <span className="ml-2">
                  {formatDate(new Date(category._creationTime))}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Category Media</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {category.imageUrl ? (
              <div>
                <h4 className="mb-2 text-sm font-medium">Image</h4>
                <div className="w-fit flex items-center justify-center rounded-sm border p-4">
                  <img
                    src={category.imageUrl}
                    alt={`${category.name} image`}
                    className="h-64 w-64 object-contain"
                  />
                </div>
              </div>
            ) : (
              <p>No media</p>
            )}
          </CardContent>
        </Card>
      </div>

      <DeleteCategoryDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        categoryName={category.name}
        onConfirm={handleDeleteCategory}
      />
    </div>
  );
}

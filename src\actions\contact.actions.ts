"use server";

import type { z } from "zod";

import { contactFormSchema } from "@/lib/schema";
import { tryCatch } from "@/utils/try-catch";

export async function submitContact(data: z.infer<typeof contactFormSchema>) {

  const validateData = contactFormSchema.safeParse(data);

  if (!validateData.success) {
    return { success: false, message: "Invalid data" };
  }

  const result = await tryCatch(new Promise((resolve) => setTimeout(resolve, 1000)))

  if (result.error) {
    return { success: false, message: result.error.message ?? "Failed to place order" };
  }

  console.log("submitContact:data", data)

  return { success: true, message: "Message sent successfully!" };

}
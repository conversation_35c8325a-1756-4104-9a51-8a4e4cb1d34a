"use server";

import type { z } from "zod";

import { checkoutFormSchema } from "@/lib/schema";
import { tryCatch } from "@/utils/try-catch";

export async function createOrder(data: z.infer<typeof checkoutFormSchema>) {
  const validateData = checkoutFormSchema.safeParse(data);

  if (!validateData.success) {
    return { success: false, message: "Invalid data" };
  }

  const result = await tryCatch(new Promise((resolve) => setTimeout(resolve, 1000)));

  if (result.error) {
    return { success: false, message: result.error.message };
  }

  console.log("createOrder:data", data);

  return { success: true, message: "Order created successfully!" };
}
"use client";

import { Heart, Trash2Icon } from "lucide-react";
import Link from "next/link";

import { Breadcrumbs } from "@/components/dashboard/breadcrumbs";
import { ProductsGrid } from "@/components/storefront/product/products-grid";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { internalLinks } from "@/config/site-config";
import { useWishlist } from "@/hooks/use-wishlist";
export default function WishlistPage() {
  const { wishlistItems, clearWishlist } = useWishlist();

  return (
    <div className="space-y-8 py-8">
      {/* Breadcrumb */}
      <Breadcrumbs
        breadcrumbs={[
          {
            title: "Home",
            href: "/"
          },
          {
            title: "Wishlist",
            href: "wishlist",
            current: true
          }
        ]}
      />

      <>
        <div className="flex flex-row justify-between items-center mb-8">
          <Typography
            variant="h2"
            weight="semibold"
            className="font-playfair text-center"
          >
            My Wishlist
          </Typography>
          {wishlistItems.length > 0 && (
            <Button
              variant="link"
              onClick={clearWishlist}
              className="hover:text-red-500 hover:no-underline"
            >
              <span className="hidden md:block">Clear Wishlist</span>
              <Trash2Icon />
            </Button>
          )}
        </div>

        {wishlistItems.length === 0 ? (
          <div className="bg-white shadow-sm py-12 rounded-lg text-center">
            <Heart size={64} className="mx-auto mb-4 text-gray-300" />
            <Typography variant="h3" className="mb-4 font-medium text-xl">
              Your wishlist is empty
            </Typography>
            <Typography variant="p" className="mb-6 text-gray-500">
              Add your favorite items to wishlist so you can purchase them
              later.
            </Typography>
            <Button asChild>
              <Link href={internalLinks.shop}>Continue Shopping</Link>
            </Button>
          </div>
        ) : (
          <>
            <ProductsGrid products={wishlistItems} />
            <div className="mt-8">
              <Button>
                <Link href={internalLinks.shop}>Continue Shopping</Link>
              </Button>
            </div>
          </>
        )}
      </>
    </div>
  );
}

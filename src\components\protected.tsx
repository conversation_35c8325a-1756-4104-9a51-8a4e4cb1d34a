"use client";

import { UserButton } from "@clerk/nextjs";
import { useQuery } from "convex/react";
import Link from "next/link";

import { api } from "@/convex/_generated/api";

export default function ProtectedContent() {
  const dbUser = useQuery(api.users.current);

  return (
    <div className="space-y-4">
      <UserButton />
      <Link href="/dashboard">Go to dashboard</Link>

      <h2>User Information from db</h2>
      {dbUser ? (
        <div className="space-y-2">
          <p>
            Name:
            {dbUser.fullName}
          </p>
          <p>
            Email:
            {dbUser.email}
          </p>
          {dbUser.imageUrl && (
            <img src={dbUser.imageUrl} alt="Profile" width={100} height={100} />
          )}
          <p>
            Created At:
            {new Date(dbUser._creationTime!).toLocaleDateString()}
          </p>
        </div>
      ) : (
        <p>Loading dbUser information...</p>
      )}
    </div>
  );
}

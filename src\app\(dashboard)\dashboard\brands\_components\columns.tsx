"use client";

import type { ColumnDef } from "@tanstack/react-table";

import Link from "next/link";

import type { Doc } from "@/convex/_generated/dataModel";

import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/utils";

import { BrandActions } from "./brand-actions";

export type Brand = Doc<"brands">;

export function columns(
  handleDelete: (id: string) => Promise<void>
): ColumnDef<Brand>[] {
  return [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <div className="font-medium">
          <Link href={`/dashboard/brands/${row.original._id}`}>
            {row.original.name}
          </Link>
        </div>
      ),
      sortingFn: "alphanumeric"
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => (
        <div className="max-w-[500px] truncate">
          {row.getValue<string>("description")}
        </div>
      ),
      sortingFn: "alphanumeric"
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => (
        <Badge
          variant="outline"
          className={
            row.original.isActive
              ? "bg-green-50 text-green-700 hover:bg-green-50"
              : "bg-amber-50 text-amber-700 hover:bg-amber-50"
          }
        >
          {row.original.isActive ? "Active" : "Inactive"}
        </Badge>
      ),
      sortingFn: "basic"
    },
    {
      accessorKey: "_creationTime",
      header: "Created Date",
      cell: ({ row }) => (
        <div>{formatDate(new Date(row.original._creationTime))}</div>
      ),
      sortingFn: "datetime"
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <BrandActions brand={row.original} onDelete={handleDelete} />
      ),
      enableSorting: false
    }
  ];
}

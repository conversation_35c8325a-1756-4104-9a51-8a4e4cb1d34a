"use client";

import type { ColumnDef } from "@tanstack/react-table";

import type { Doc } from "@/convex/_generated/dataModel";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ROLES } from "@/convex/permissions";
import { cn, formatDate } from "@/utils";

import { UserActions } from "./user-actions";

export type User = Doc<"users"> & {
  role: string;
};

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: "imageUrl",
    header: "",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <Avatar>
          <AvatarImage
            src={user.imageUrl || "/placeholder.svg?height=40&width=40"}
            alt={user.firstName}
          />
          <AvatarFallback>
            {user.firstName
              .split(" ")
              .map((n) => n[0])
              .join("")}
          </AvatarFallback>
        </Avatar>
      );
    }
  },
  {
    accessorKey: "firstName",
    header: "First Name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("firstName")}</div>
    ),
    sortingFn: "alphanumeric"
  },
  {
    accessorKey: "lastName",
    header: "Last Name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("lastName")}</div>
    ),
    sortingFn: "alphanumeric"
  },
  {
    accessorKey: "email",
    header: "Email"
  },
  {
    accessorKey: "role",
    header: "Role",
    cell: ({ row }) => {
      const role = row.getValue<string>("role");
      return (
        <Badge
          variant="outline"
          className={cn(
            "rounded-sm",
            "bg-gray-50 text-gray-700 hover:bg-gray-50",
            {
              "bg-red-50 text-red-700 hover:bg-red-50": role === ROLES.ADMIN,
              "bg-blue-50 text-blue-700 hover:bg-blue-50": role === ROLES.EDITOR
            }
          )}
        >
          {role}
        </Badge>
      );
    }
  },
  {
    accessorKey: "_creationTime",
    header: "Joined On",
    cell: ({ row }) => (
      <div>{formatDate(new Date(row.original._creationTime))}</div>
    ),
    sortingFn: "datetime"
  },
  {
    id: "actions",
    cell: ({ row }) => <UserActions user={row.original} />
  }
];

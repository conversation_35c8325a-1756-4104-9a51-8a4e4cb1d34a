"use server"

import { env } from "@/config/env.mjs";
import { tryCatch } from "@/utils/try-catch";

type UploadImageResponse = {
  data: {
    imageUrl: string;
    storageId: string;
  } | null;
  message: string;
  success: boolean;
};
export const uploadImage = async (file: File): Promise<UploadImageResponse> => {
  if (!file) {
    return {
      data: null,
      message: "No file provided",
      success: false,
    };
  }

  const sendImageUrl = new URL(`${env.CONVEX_SITE_URL}/sendImage`);

  const { data, success, error } = await tryCatch(fetch(sendImageUrl, {
    method: "POST",
    headers: { "Content-Type": file!.type },
    body: file,
  }))

  if (!success) {
    console.error(error);
    return {
      data: null,
      message: "Error uploading image",
      success: false,
    };
  }

  return {
    data: await data.json(),
    message: "Image uploaded successfully",
    success: true,
  }
}




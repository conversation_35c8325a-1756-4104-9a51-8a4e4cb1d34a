"use client";

import type { ReactNode } from "react";

import { redirect } from "next/navigation";

import { AppSidebar } from "@/components/dashboard/layout/app-sidebar";
import DashboardHeader from "@/components/dashboard/layout/dashboard-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { internalLinks } from "@/config/site-config";
import { useAuth } from "@/hooks/use-auth";

export default function DashboardLayout({ children }: { children: ReactNode }) {
  const { user, loaded } = useAuth();

  if (!loaded) {
    return null;
  }

  if (loaded && user === null) {
    redirect(internalLinks.home);
  }
  return (
    <SidebarProvider defaultOpen={true}>
      <AppSidebar variant="inset" className="sticky" />
      <SidebarInset>
        <DashboardHeader />
        <div className="flex-1 overflow-auto">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}

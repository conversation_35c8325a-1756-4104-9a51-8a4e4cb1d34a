"use client";

import { HeartIcon } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useWishlist } from "@/hooks/use-wishlist";

export function FavoriteIcon() {
  const { wishlistItems } = useWishlist();
  const wishlistItemsCount = wishlistItems.length;

  return (
    <Button asChild variant="link" size="icon">
      <Link href="/wishlist" className="relative hover:text-primary">
        <HeartIcon className="size-6" />
        {wishlistItemsCount > 0 && (
          <span className="-top-1 -right-1 absolute flex justify-center items-center bg-primary rounded-full w-5 h-5 text-[10px] text-background">
            {wishlistItemsCount}
          </span>
        )}
      </Link>
    </Button>
  );
}

"use client";

import { useMutation, useQuery } from "convex/react";
import { Plus } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { DataTable } from "@/components/dashboard/data-table";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { tryCatch } from "@/utils/try-catch";

import { columns } from "./_components/columns";

export default function BrandsPage() {
  const brands = useQuery(api.brands.list, { isActive: undefined });
  const deleteBrand = useMutation(api.brands.remove);
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (!brands) {
    return <Loading />;
  }

  const handleDeleteBrand = async (id: string) => {
    const { success, error } = await tryCatch(
      deleteBrand({ id: id as Id<"brands"> })
    );

    if (success) {
      toast.success("Brand deleted");
    } else {
      toast.error("Failed to delete brand", {
        description: error.message
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-start justify-between">
        <PageSubHeader
          title="Brands"
          description="Manage product brands"
          breadcrumbs={[
            { title: "Dashboard", href: "/dashboard" },
            { title: "Brands", current: true }
          ]}
        />

        <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
          <Link href="/dashboard/brands/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Brand
            </Button>
          </Link>
        </RoleGate>
      </div>

      <DataTable
        columns={columns(handleDeleteBrand)}
        data={brands}
        searchKey="name"
        searchPlaceholder="Search brands..."
      />
    </div>
  );
}

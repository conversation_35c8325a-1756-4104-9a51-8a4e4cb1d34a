import { v } from "convex/values";

import { mutation, query } from "./functions";
import { PERMISSIONS, viewerHasPermissionX, } from "./permissions";
import { slugify } from "./utils";

export const get = query({
  args: {
    id: v.id("brands"),
  },
  handler: async (ctx, args) => {
    const brand = await ctx.table("brands").getX(args.id);

    return brand;
  },
});

export const list = query({
  args: {
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.table("brands");
    if (args.isActive) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    return await query;
  },
});

export const metadata = query({
  handler: async (ctx) => {
    const brands = await ctx.table("brands");

    return {
      count: brands.length,
      active: brands.filter((brand) => brand.isActive).length,
    };
  }
})

export const create = mutation({
  args: {
    name: v.string(),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    logoUrl: v.optional(v.string()),
    logoId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.brands.update);
    const brand = await ctx.table("brands").insert({
      ...args,
      slug: args.slug ?? slugify(args.name),
      isActive: args.isActive ?? true,
    }).get();

    return brand;
  },
});

export const update = mutation({
  args: {
    id: v.id("brands"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
    logoId: v.optional(v.id("_storage")),
    slug: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.brands.update);
    const { id, ...data } = args;
    const updatedBrand = await ctx.table("brands").getX(id).patch(data).get();

    return updatedBrand;
  },
});

export const remove = mutation({
  args: {
    id: v.id("brands"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.brands.delete);
    return await ctx.table("brands").getX(args.id).delete();
  },
});

export const removeLogo = mutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.brands.update);

    await ctx.storage.delete(args.storageId);
    await ctx.table("brands").getX("logoId", args.storageId).patch({ logoUrl: undefined, logoId: undefined });

  }
})

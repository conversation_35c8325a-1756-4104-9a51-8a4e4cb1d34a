"use client";

import { useQuery } from "convex/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { BrandForm } from "../../_components/brand-form";

export default function EditBrandPage() {
  const { id: brandId } = useParams();
  const brand = useQuery(api.brands.get, { id: brandId as Id<"brands"> });
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }

  if (!brand) {
    return <Loading />;
  }

  return (
    <RoleGate
      allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]}
      redirectTo="/dashboard/brands"
    >
      <div className="flex flex-col gap-4 p-4">
        <div className="flex items-center justify-between">
          <PageSubHeader
            title={`Edit ${brand.name}`}
            description="Update brand information"
            breadcrumbs={[
              { title: "Dashboard", href: "/dashboard" },
              { title: "Brands", href: "/dashboard/brands" },
              { title: brand.name, href: `/dashboard/brands/${brand._id}` },
              { title: "Edit", current: true }
            ]}
          />
          <Button variant="outline" asChild>
            <Link href={`/dashboard/brands/${brand._id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Brand
            </Link>
          </Button>
        </div>

        <BrandForm initialData={brand} brandId={brand._id} />
      </div>
    </RoleGate>
  );
}

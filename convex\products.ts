import type { PaginationOptions } from "convex/server";

import { paginationOptsValidator } from "convex/server";
import { v } from "convex/values";

import { LOW_STOCK_THRESHOLD, PER_PAGE } from "./../src/config/constants";
import { mutation, query } from "./functions";
import { PERMISSIONS, viewerHasPermissionX } from "./permissions";
import { slugify } from "./utils";

export const get = query({
  args: {
    id: v.id("products"),
  },
  handler: async (ctx, args) => {
    const product = await ctx.table("products").getX(args.id);
    const [category, brand, scent, images] = await Promise.all([
      product.edge("category"),
      product.edge("brand"),
      product.edge("scent"),
      product.edge("images"),
    ]);

    return {
      ...product,
      category,
      brand,
      scent,
      images,
    };
  },
});

export const getBySlug = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    const product = await ctx.table("products").getX("slug", args.slug);
    const [category, brand, scent, images] = await Promise.all([
      product.edge("category"),
      product.edge("brand"),
      product.edge("scent"),
      product.edge("images"),
    ]);

    return {
      ...product,
      category,
      brand,
      scent,
      images,
    };
  },
});

export const search = query({
  args: {
    query: v.string(),
    paginationOpts: v.optional(paginationOptsValidator),

  },
  handler: async (ctx, args) => {
    const query = ctx.table("products").search("search", q => q.search("name", args.query.toLowerCase()));
    const defaultPaginationOpts: PaginationOptions = { numItems: PER_PAGE, cursor: null };
    const productsResult = await query.paginate({ ...defaultPaginationOpts, ...(args.paginationOpts || {}) });

    const products = await Promise.all(
      productsResult.page.map(async (product) => {
        return {
          ...product,
          category: await product.edge("category"),
          brand: await product.edge("brand"),
          scent: await product.edge("scent"),
          images: await product.edge("images"),
        };
      })
    );

    return {
      ...productsResult,
      products,
    };
  },
});

export const list = query({
  args: {
    isPublished: v.optional(v.boolean()),
    isNew: v.optional(v.boolean()),
    onSale: v.optional(v.boolean()),
    featured: v.optional(v.boolean()),
    categorySlug: v.optional(v.string()),
    categoryId: v.optional(v.id("categories")),
    brandSlug: v.optional(v.string()),
    brandId: v.optional(v.id("brands")),
    scentSlug: v.optional(v.string()),
    scentId: v.optional(v.id("scents")),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    inStock: v.optional(v.boolean()),
    sortBy: v.optional(v.union(
      v.literal("price_asc"),
      v.literal("price_desc"),
      v.literal("newest"),
      v.literal("oldest")
    )),
    paginationOpts: v.optional(paginationOptsValidator),
  },
  handler: async (ctx, args) => {
    let query = ctx.table("products");

    // Apply filters
    if (args.isPublished !== undefined) {
      query = query.filter(q => q.eq(q.field("isPublished"), args.isPublished));
    }
    if (args.isNew !== undefined) {
      query = query.filter(q => q.eq(q.field("isNew"), args.isNew));
    }
    if (args.onSale !== undefined) {
      query = query.filter(q => q.eq(q.field("sale.active"), args.onSale));
    }
    if (args.featured !== undefined) {
      query = query.filter(q => q.eq(q.field("featured"), args.featured));
    }

    // Apply category filter
    if (args.categorySlug !== undefined) {
      const category = await ctx.table("categories").get("slug", args.categorySlug);
      if (category) {
        query = query.filter(q => q.eq(q.field("categoryId"), category._id));
      }
    }
    if (args.categoryId !== undefined) {
      query = query.filter(q => q.eq(q.field("categoryId"), args.categoryId));
    }

    // Apply brand filter
    if (args.brandSlug !== undefined) {
      const brand = await ctx.table("brands").get("slug", args.brandSlug);
      if (brand) {
        query = query.filter(q => q.eq(q.field("brandId"), brand._id));
      }
    }
    if (args.brandId !== undefined) {
      query = query.filter(q => q.eq(q.field("brandId"), args.brandId));
    }

    // Apply scent filter
    if (args.scentSlug !== undefined) {
      const scent = await ctx.table("scents").get("slug", args.scentSlug);
      if (scent) {
        query = query.filter(q => q.eq(q.field("scentId"), scent._id));
      }
    }
    if (args.scentId !== undefined) {
      query = query.filter(q => q.eq(q.field("scentId"), args.scentId));
    }
    if (args.minPrice !== undefined) {
      query = query.filter(q => q.gte(q.field("price"), args.minPrice!));
    }
    if (args.maxPrice !== undefined) {
      query = query.filter(q => q.lte(q.field("price"), args.maxPrice!));
    }
    if (args.inStock !== undefined) {
      if (args.inStock) {
        query = query.filter(q => q.gt(q.field("stock"), 0));
      } else {
        query = query.filter(q => q.eq(q.field("stock"), 0));
      }
    }

    // Apply sorting
    if (args.sortBy) {
      switch (args.sortBy) {
        case "price_asc":
          query.order("asc", "price");
          break;
        case "price_desc":
          query.order("desc", "price");
          break;
        case "newest":
          query.order("desc");
          break;
        case "oldest":
          query.order("asc");
          break;
      }
    }

    const defaultPaginationOpts: PaginationOptions = { numItems: PER_PAGE, cursor: null };
    const productsResult = await query.paginate({ ...defaultPaginationOpts, ...(args.paginationOpts || {}) });

    const products = await Promise.all(
      productsResult.page.map(async (product) => {
        return {
          ...product,
          category: await product.edge("category"),
          brand: await product.edge("brand"),
          scent: await product.edge("scent"),
          images: await product.edge("images"),
        };
      })
    );

    return {
      ...productsResult,
      products,
    };
  },
});

export const lowStock = query({
  args: {
    paginationOpts: v.optional(paginationOptsValidator),
  },
  handler: async (ctx, args) => {
    const query = ctx.table("products").filter(q => q.lte(q.field("stock"), LOW_STOCK_THRESHOLD));
    const defaultPaginationOpts: PaginationOptions = { numItems: PER_PAGE, cursor: null };
    const productsResult = await query.paginate({ ...defaultPaginationOpts, ...(args.paginationOpts || {}) });
    return {
      ...productsResult,
      products: await Promise.all(
        productsResult.page.map(async (product) => {
          return {
            ...product,
            category: await product.edge("category"),
            brand: await product.edge("brand"),
            scent: await product.edge("scent"),
            images: await product.edge("images"),
          };
        })
      ),
    };
  }
})

export const metadata = query({
  handler: async (ctx) => {
    const products = await ctx.table("products");

    return {
      count: products.length,
      published: products.filter(p => p.isPublished).length,
    };
  }


})

export const create = mutation({
  args: {
    name: v.string(),
    slug: v.optional(v.string()),
    shortDescription: v.string(),
    description: v.string(),
    regularPrice: v.number(),
    sale: v.optional(v.object({
      active: v.boolean(),
      price: v.number(),
      discountPercentage: v.number(),
      originalPrice: v.number(),
      startDate: v.number(),
      endDate: v.optional(v.number()),
    })),
    stock: v.number(),
    categoryId: v.id("categories"),
    brandId: v.id("brands"),
    scentId: v.optional(v.id("scents")),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.products.update);
    const price = args.sale?.active ? args.sale?.price : args.regularPrice;
    const product = await ctx.table("products").insert({
      ...args,
      price,
      slug: args.slug ?? slugify(args.name),
      isPublished: true,
      isNew: true,
      featured: false,
      updatedAt: Date.now(),
    }).get();

    const [category, brand, scent, images] = await Promise.all([
      product.edge("category"),
      product.edge("brand"),
      product.edge("scent"),
      product.edge("images"),
    ]);

    return {
      ...product,
      category,
      brand,
      scent,
      images,
    };
  },
});

export const update = mutation({
  args: {
    id: v.id("products"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    shortDescription: v.optional(v.string()),
    description: v.optional(v.string()),
    regularPrice: v.optional(v.number()),
    sale: v.optional(v.object({
      active: v.boolean(),
      price: v.number(),
      discountPercentage: v.number(),
      originalPrice: v.number(),
      startDate: v.number(),
      endDate: v.optional(v.number()),
    })),
    stock: v.optional(v.number()),
    categoryId: v.optional(v.id("categories")),
    brandId: v.optional(v.id("brands")),
    scentId: v.optional(v.id("scents")),
    isPublished: v.optional(v.boolean()),
    isNew: v.optional(v.boolean()),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.products.update);
    const { id, ...data } = args;
    const price = args.sale?.active ? args.sale?.price : args.regularPrice;
    const product = await ctx.table("products").getX(id).patch({
      ...data,
      price,
      updatedAt: Date.now(),
    }).get();

    const [category, brand, scent, images] = await Promise.all([
      product.edge("category"),
      product.edge("brand"),
      product.edge("scent"),
      product.edge("images"),
    ]);

    return {
      ...product,
      category,
      brand,
      scent,
      images,
    };
  },
});

export const remove = mutation({
  args: {
    id: v.id("products"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.products.delete);
    const product = await ctx.table("products").getX(args.id);

    const images = await product.edge("images");
    await Promise.all(images.map(image => ctx.table("images").getX(image._id).delete()));

    return await product.delete();
  },
});

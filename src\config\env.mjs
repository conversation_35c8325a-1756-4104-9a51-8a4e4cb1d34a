/* eslint-disable node/no-process-env */
import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  client: {
    NEXT_PUBLIC_CONVEX_URL: z.string(),
    NEXT_PUBLIC_BASE_URL: z.string(),
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string()
  },
  server: {
    CONVEX_DEPLOYMENT: z.string(),
    CONVEX_SITE_URL: z.string(),
    CLIENT_ORIGIN: z.string(),
    ADMIN_EMAIL: z.string(),
    CLERK_SECRET_KEY: z.string(),
    CLERK_JWT_ISSUER_DOMAIN: z.string(),
    CLERK_WEBHOOK_SECRET: z.string(),
    TG_BOT_TOKEN: z.string(),
    TG_ADMIN_CHAT_ID: z.string(),
    TG_CHAT_IDS: z.string()
  },
  runtimeEnv: {
    CONVEX_DEPLOYMENT: process.env.CONVEX_DEPLOYMENT,
    CONVEX_SITE_URL: process.env.CONVEX_SITE_URL,
    NEXT_PUBLIC_CONVEX_URL: process.env.NEXT_PUBLIC_CONVEX_URL,
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
    CLIENT_ORIGIN: process.env.CLIENT_ORIGIN,
    ADMIN_EMAIL: process.env.ADMIN_EMAIL,
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:
      process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
    CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,
    CLERK_JWT_ISSUER_DOMAIN: process.env.CLERK_JWT_ISSUER_DOMAIN,
    CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET,
    TG_BOT_TOKEN: process.env.TG_BOT_TOKEN,
    TG_ADMIN_CHAT_ID: process.env.TG_ADMIN_CHAT_ID,
    TG_CHAT_IDS: process.env.TG_CHAT_IDS
  },
  skipValidation: !!process.env.CI || !!process.env.SKIP_ENV_VALIDATION
});

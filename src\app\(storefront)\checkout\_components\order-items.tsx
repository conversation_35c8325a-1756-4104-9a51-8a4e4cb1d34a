"use client";

import React from "react";

import { CartItem } from "@/components/storefront/cart/car-item";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { useCart } from "@/hooks/use-cart";
import { convertToLocale } from "@/utils/money";

export default function OrderItems({ className }: { className?: string }) {
  const { cartItems, totalAmount } = useCart();

  return (
    <Card className={`w-full bg-background h-fit ${className}`}>
      <CardHeader>
        <Typography variant="h2">Your Order</Typography>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {cartItems.map((item) => (
            <CartItem key={item._id} item={item} showControls={false} />
          ))}
        </div>

        <div className="mt-6 pt-6 border-gray-200 border-t">
          <div className="flex justify-between mb-4">
            <Typography variant="p">Subtotal</Typography>
            <Typography variant="p">
              {convertToLocale({ amount: totalAmount })}
            </Typography>
          </div>

          <div className="flex justify-between mt-4 font-medium text-lg">
            <Typography variant="p">Total</Typography>
            <Typography variant="price">
              {convertToLocale({ amount: totalAmount })}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

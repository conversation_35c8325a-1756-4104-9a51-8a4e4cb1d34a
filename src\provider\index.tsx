import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";

import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/lib/context/auth";

import { ConvexProvider } from "./convex-client-provider";

function AppProvider({ children }: { children: React.ReactNode }) {
  return (
    <ClerkProvider>
      <ConvexProvider>
        <AuthProvider>
          <TooltipProvider>{children}</TooltipProvider>
        </AuthProvider>
      </ConvexProvider>
    </ClerkProvider>
  );
}

export default AppProvider;

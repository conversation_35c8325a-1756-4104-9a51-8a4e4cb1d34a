"use client";

import { ShoppingCart } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { useCart } from "@/hooks/use-cart";

import { CartSheet } from "../../cart/cart-sheet";

export function CartIcon() {
  const { cartItems, openCart } = useCart();

  const cartItemsCount = cartItems.reduce(
    (acc, item) => acc + item.quantity,
    0
  );

  return (
    <>
      <Button
        variant="link"
        size="icon"
        onClick={openCart}
        className="relative hover:text-primary"
      >
        <ShoppingCart className="size-6" />
        {cartItemsCount > 0 && (
          <span className="-top-1 -right-1 absolute flex justify-center items-center bg-primary rounded-full w-5 h-5 text-[10px] text-background">
            {cartItemsCount}
          </span>
        )}
      </Button>
      <CartSheet />
    </>
  );
}

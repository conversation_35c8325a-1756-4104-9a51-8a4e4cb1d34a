"use client";

import type React from "react";

import {
  BarChart3,
  Box,
  Briefcase,
  Droplets,
  ShoppingBag,
  Tag,
  Users
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail
} from "@/components/ui/sidebar";
import { internalLinks, siteInfo } from "@/config/site-config";
import { PERMISSIONS, ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import { SidebarNavMain } from "./sidebar-nav-main";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, hasPermission } = useAuth();
  const pathname = usePathname();

  const navItems = useMemo(() => {
    if (!user) return [];

    const items: any = [
      {
        title: "Dashboard",
        url: "/dashboard",
        icon: BarChart3,
        isActive: pathname === "/dashboard",
        items: []
      }
    ];

    // Products section - visible to all roles
    if (hasPermission(PERMISSIONS.products.read)) {
      items.push({
        title: "Products",
        url: "/dashboard/products",
        icon: ShoppingBag,
        isActive: pathname.startsWith("/dashboard/products"),
        items: [
          {
            title: "All Products",
            url: "/dashboard/products",
            isActive: pathname === "/dashboard/products"
          },
          ...(hasPermission(PERMISSIONS.products.update)
            ? [
                {
                  title: "Add New",
                  url: "/dashboard/products/new",
                  isActive: pathname === "/dashboard/products/new"
                }
              ]
            : [])
        ]
      });
    }

    // Categories section
    if (hasPermission(PERMISSIONS.categories.read)) {
      items.push({
        title: "Categories",
        url: "/dashboard/categories",
        icon: Tag,
        isActive: pathname.startsWith("/dashboard/categories"),
        items: [
          {
            title: "All Categories",
            url: "/dashboard/categories",
            isActive: pathname === "/dashboard/categories"
          },
          ...(hasPermission(PERMISSIONS.categories.update)
            ? [
                {
                  title: "Add New",
                  url: "/dashboard/categories/new",
                  isActive: pathname === "/dashboard/categories/new"
                }
              ]
            : [])
        ]
      });
    }

    // Brands section
    if (hasPermission(PERMISSIONS.brands.read)) {
      items.push({
        title: "Brands",
        url: "/dashboard/brands",
        icon: Briefcase,
        isActive: pathname.startsWith("/dashboard/brands"),
        items: [
          {
            title: "All Brands",
            url: "/dashboard/brands",
            isActive: pathname === "/dashboard/brands"
          },
          ...(hasPermission(PERMISSIONS.brands.update)
            ? [
                {
                  title: "Add New",
                  url: "/dashboard/brands/new",
                  isActive: pathname === "/dashboard/brands/new"
                }
              ]
            : [])
        ]
      });
    }

    // Scents section
    if (hasPermission(PERMISSIONS.scents.read)) {
      items.push({
        title: "Scents",
        url: "/dashboard/scents",
        icon: Droplets,
        isActive: pathname.startsWith("/dashboard/scents"),
        items: [
          {
            title: "All Scents",
            url: "/dashboard/scents",
            isActive: pathname === "/dashboard/scents"
          },
          ...(hasPermission(PERMISSIONS.scents.update)
            ? [
                {
                  title: "Add New",
                  url: "/dashboard/scents/new",
                  isActive: pathname === "/dashboard/scents/new"
                }
              ]
            : [])
        ]
      });
    }

    // Users section - only for Admin
    if (user.role.name === ROLES.ADMIN) {
      items.push({
        title: "Users",
        url: "/dashboard/users",
        icon: Users,
        isActive: pathname.startsWith("/dashboard/users"),
        items: []
      });
    }

    return items;
  }, [pathname, user, hasPermission]);

  if (!user) return null;

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              size="lg"
              className="flex items-center gap-2 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Link href={internalLinks.home}>
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                  <Box className="size-4" />
                </div>
                <div className="flex flex-col gap-0.5 leading-none">
                  <span className="font-semibold">{siteInfo.name}</span>
                  <span className="text-xs text-muted-foreground">
                    Go to Store front
                  </span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarNavMain items={navItems} />
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}

import type { ClerkMiddlewareAuth } from "@clerk/nextjs/server";

import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { createI18nMiddleware } from "next-international/middleware";
import { NextResponse } from "next/server";

const I18nMiddleware = createI18nMiddleware({
  locales: ["en", "fr"],
  defaultLocale: "en",
  urlMappingStrategy: "rewrite",
});

const isSignInRoute = createRouteMatcher(["/sign-in"]);

export default clerkMiddleware(async (auth: ClerkMiddlewareAuth, req) => {
  const { userId, redirectToSignIn } = await auth();

  const isSignIn = isSignInRoute(req);
  if (isSignIn && userId) {
    console.log("redirecting to /", {
      isSignIn,
      userId,
    });
    return NextResponse.redirect(new URL("/", req.url));
  }
  if (!isSignIn && !userId) {
    console.log("redirecting to /sign-in", {
      isSignIn,
      userId,
    });
    return redirectToSignIn();
  }

  console.log("no redirect", {
    isSignIn,
    userId,
  });

  return I18nMiddleware(req);
});

export const config = {
  matcher: [
    "/((?!_next/static|api|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",

    // all routes except static assets
    "/((?!.*\\..*|_next).*)",
    "/",
    "/(api|trpc)(.*)",
  ],
};

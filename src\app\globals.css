@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
@import 'tailwindcss';
@import 'tw-animate-css';

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1884 0.0128 248.5103);
  --card: oklch(0.9784 0.0011 197.1387);
  --card-foreground: oklch(0.1884 0.0128 248.5103);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.1884 0.0128 248.5103);
  --primary: oklch(0.3935 0.0689 336.0195);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(1 0 0);
  --secondary-foreground: oklch(0.1884 0.0128 248.5103);
  --muted: oklch(0.9222 0.0013 286.3737);
  --muted-foreground: oklch(0.1884 0.0128 248.5103);
  --accent: oklch(0.944 0.0058 333.9818);
  --accent-foreground: oklch(0.3935 0.0689 336.0195);
  --destructive: oklch(0.6188 0.2376 25.7658);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.946 0.0042 337.3542);
  --input: oklch(0.946 0.0042 337.3542);
  --ring: oklch(0.3935 0.0689 336.0195);
  --chart-1: oklch(0.3935 0.0689 336.0195);
  --chart-2: oklch(0.6907 0.1554 160.3454);
  --chart-3: oklch(0.8214 0.16 82.5337);
  --chart-4: oklch(0.7064 0.1822 151.7125);
  --chart-5: oklch(0.5919 0.2186 10.5826);
  --sidebar: oklch(0.9784 0.0011 197.1387);
  --sidebar-foreground: oklch(0.1884 0.0128 248.5103);
  --sidebar-primary: oklch(0.3935 0.0689 336.0195);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.944 0.0058 333.9818);
  --sidebar-accent-foreground: oklch(0.3935 0.0689 336.0195);
  --sidebar-border: oklch(0.946 0.0042 337.3542);
  --sidebar-ring: oklch(0.3935 0.0689 336.0195);
  --radius: 0.625rem;
  --shadow-2xs: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0);
  --shadow-xs: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0);
  --shadow-sm: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0), 2px 1px 2px 0px hsl(315 11.1111% 92.9412% / 0);
  --shadow: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0), 2px 1px 2px 0px hsl(315 11.1111% 92.9412% / 0);
  --shadow-md: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0), 2px 2px 4px 0px hsl(315 11.1111% 92.9412% / 0);
  --shadow-lg: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0), 2px 4px 6px 0px hsl(315 11.1111% 92.9412% / 0);
  --shadow-xl: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0), 2px 8px 10px 0px hsl(315 11.1111% 92.9412% / 0);
  --shadow-2xl: 2px 2px 0px 1px hsl(315 11.1111% 92.9412% / 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-playfair: var(--font-playfair-display);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

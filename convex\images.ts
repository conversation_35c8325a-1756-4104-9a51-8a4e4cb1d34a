import { v } from "convex/values";

import { mutation, query } from "./functions";
import { PERMISSIONS, viewerHasPermissionX } from "./permissions";

export const get = query({
  args: {
    id: v.id("images"),
  },
  handler: async (ctx, args) => {

    return await ctx.table("images").getX(args.id)
  },
});

export const list = query({
  args: {
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, args) => {
    let query = ctx.table("images");
    if (args.productId) {
      query = query.filter(q => q.eq(q.field("productId"), args.productId));
    }

    return await query
  }

});



export const create = mutation({
  args: {
    imageUrl: v.string(),
    imageId: v.id("_storage"),
    isPrimary: v.optional(v.boolean()),
    productId: v.id("products"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.products.update);

    const existingImages = await ctx.table("images")
      .filter(q => q.eq(q.field("productId"), args.productId));

    if (existingImages.length === 0 || args.isPrimary) {
      if (args.isPrimary) {
        for (const image of existingImages) {
          if (image.isPrimary) {
            await ctx.table("images").getX(image._id).patch({
              isPrimary: false,
            });
          }
        }
      }
    }

    const image = await ctx.table("images").insert({ ...args, isPrimary: args.isPrimary || false }).get();

    return image;
  },
});

export const update = mutation({
  args: {
    id: v.id("images"),
    isPrimary: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.products.update);
    const { id, isPrimary } = args;
    const image = await ctx.table("images").getX(id);

    if (isPrimary) {
      const otherImages = await ctx.table("images")
        .filter(q =>
          q.and(
            q.eq(q.field("productId"), image.productId),
            q.neq(q.field("_id"), id)
          )
        );

      for (const otherImage of otherImages) {
        if (otherImage.isPrimary) {
          await ctx.table("images").getX(otherImage._id).patch({
            isPrimary: false,
          });
        }
      }
    }

    const updatedImage = await image.patch({ isPrimary }).get();

    return updatedImage
  },
});

export const remove = mutation({
  args: {
    id: v.id("images"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.products.update);
    const image = await ctx.table("images").getX(args.id);

    if (image.isPrimary && image.productId) {
      const otherImages = await ctx.table("images")
        .filter(q =>
          q.and(
            q.eq(q.field("productId"), image.productId),
            q.neq(q.field("_id"), args.id)
          )
        )
        .order("asc");

      if (otherImages.length > 0) {
        await ctx.table("images").getX(otherImages[0]._id).patch({
          isPrimary: true,
        });
      }
    }
    return await image.delete();
  },
});


export const getUrlByStorageId = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  }
})
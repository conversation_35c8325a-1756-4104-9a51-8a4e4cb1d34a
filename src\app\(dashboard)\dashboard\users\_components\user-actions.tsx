"use client";

import { useMutation } from "convex/react";
import { MoreHorizontal, Shield } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { api } from "@/convex/_generated/api";
import { PERMISSIONS, ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { tryCatch } from "@/utils/try-catch";

import type { User } from "./columns";

type UserActionsProps = {
  user: User;
};

export function UserActions({ user }: UserActionsProps) {
  const { user: currentUser, hasPermission } = useAuth();
  const [showRoleDialog, setShowRoleDialog] = useState(false);

  const canChangeRole = hasPermission(PERMISSIONS.users.update);

  const isSelf = currentUser?._id === user._id;

  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          {canChangeRole && !isSelf && (
            <DropdownMenuItem onClick={() => setShowRoleDialog(true)}>
              <Shield className="mr-2 h-4 w-4" />
              Change Role
            </DropdownMenuItem>
          )}
          <ChangeRoleDialog
            open={showRoleDialog}
            onOpenChange={setShowRoleDialog}
            user={user}
          />
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

const ChangeRoleDialog = ({
  open,
  onOpenChange,
  user
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User;
}) => {
  const changeUserRole = useMutation(api.users.changeRole);
  const [selectedRole, setSelectedRole] = useState(user.role);
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);

  const handleRoleChange = async () => {
    setIsUpdatingRole(true);
    const { success, error } = await tryCatch(
      changeUserRole({
        userId: user._id,
        roleName: selectedRole
      })
    );

    if (success) {
      toast.success(
        `${user.firstName}'s role has been updated to ${selectedRole}.`
      );
      onOpenChange(false);
    } else {
      toast.error(error?.message || "Failed to update user role.");
    }
    setIsUpdatingRole(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change User Role</DialogTitle>
          <DialogDescription>
            Change the role for {user.firstName}. This will affect their
            permissions in the system.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select
              value={selectedRole}
              onValueChange={(value) => setSelectedRole(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(ROLES).map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUpdatingRole}
          >
            Cancel
          </Button>
          <Button onClick={handleRoleChange} disabled={isUpdatingRole}>
            {isUpdatingRole ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

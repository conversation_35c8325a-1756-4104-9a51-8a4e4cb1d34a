"use client";

import { useQuery } from "convex/react";
import { Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

import type { Product } from "@/types";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import { Typography } from "@/components/ui/typography";
import { api } from "@/convex/_generated/api";
import { useDebounce } from "@/hooks/use-debounce";
import { convertToLocale } from "@/utils/money";

export function SearchPopover({ className }: { className?: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedQuery = useDebounce(searchQuery, 1000);
  const productResults = useQuery(
    api.products.search,
    debouncedQuery.trim().length < 3 ? "skip" : { query: debouncedQuery }
  );
  const router = useRouter();

  const handleSearch = () => {
    if (searchQuery.trim().length < 3) return;

    router.push(`/search?query=${encodeURIComponent(searchQuery)}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    if (e.target.value.trim().length < 3) {
      setIsOpen(false);
      return;
    }

    setIsOpen(true);
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (searchQuery.trim().length < 3) return;

    setIsOpen(true);

    console.log("Button clicked!");
  };

  const handleViewAllResults = () => {
    if (searchQuery.trim().length < 3) return;

    router.push(`/search?query=${encodeURIComponent(searchQuery)}`);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild onClick={handleClick} className={className}>
        <div className="relative bg-muted/50 rounded-full">
          <Input
            className="pe-20 lg:pe-9 rounded-full placeholder:text-muted-foreground/50"
            placeholder="Search"
            value={searchQuery}
            onChange={handleInputChange}
          />
          <Button
            className="top-0 right-0 absolute"
            variant="link"
            size="icon"
            aria-label="Search for products"
            onClick={handleSearch}
          >
            <Search size={16} aria-hidden="true" />
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 rounded-none w-full min-w-2xs"
        sideOffset={1}
      >
        {searchQuery.trim().length > 2 ? (
          productResults && productResults.products.length > 0 ? (
            <div className="flex flex-col gap-2">
              <Typography as="h2" variant="h6" className="px-4 pt-2 uppercase">
                Products{" "}
              </Typography>

              {productResults.products.map((product) => (
                <SearchProductCard key={product._id} product={product} />
              ))}

              {productResults.pageStatus?.includes("more") && (
                <Button
                  variant="link"
                  className="justify-start w-full"
                  onClick={handleViewAllResults}
                >
                  View all results
                </Button>
              )}
            </div>
          ) : productResults === undefined ? (
            <div className="flex justify-center p-4">
              <Typography variant="smallText">Loading...</Typography>
            </div>
          ) : (
            <div className="flex justify-center p-4">
              <Typography variant="smallText">No products found</Typography>
            </div>
          )
        ) : (
          <div className="flex justify-center p-4">
            <Typography variant="smallText">
              Start typing to search to product
            </Typography>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}

function SearchProductCard({ product }: { product: Product }) {
  return (
    <Link
      href={`/products/${product.slug}`}
      className="flex items-center gap-4 hover:bg-muted/30 px-8 py-2 transition-colors duration-300"
    >
      <div className="w-[30%]">
        <Image
          width={64}
          height={64}
          className="object-cover aspect-square"
          src={product.images[0].imageUrl}
          alt={product.name}
        />
      </div>
      <div className="flex flex-col gap-1 col-span-8">
        <Typography variant="mutedText" className="text-xs uppercase">
          {product.brand.name}
        </Typography>
        <Typography as="h3" variant="h5" className="font-playfair">
          {product.name}
        </Typography>
        <div className="flex gap-1">
          <Typography weight="medium" className="text-primary">
            {convertToLocale({ amount: product.price })}
          </Typography>
          {product.sale?.active && (
            <Typography
              variant="mutedText"
              weight="medium"
              className="line-through"
            >
              {convertToLocale({ amount: product.sale.originalPrice })}
            </Typography>
          )}
        </div>
      </div>
    </Link>
  );
}

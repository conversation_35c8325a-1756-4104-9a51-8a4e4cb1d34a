import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@clerk/backend";
import type { <PERSON><PERSON><PERSON> } from "convex/values";

import { v } from "convex/values";

import type { MutationCtx, QueryCtx } from "./types";

import { internalMutation, mutation, query } from "./functions";
import { getRole, getRolePermissions, PERMISSIONS, ROLES, viewerHasPermissionX } from "./permissions";

// eslint-disable-next-line node/no-process-env
const ADMIN_EMAIL = process.env.ADMIN_EMAIL;

export const current = query({
  args: {},
  handler: async (ctx) => {
    return await getCurrentUser(ctx);
  },
});

export const userWithPermissions = query({
  args: {
  },
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user)
      throw new Error("User not found");
    const permissions = await getRolePermissions(ctx, user.role.name);
    return { user, permissions: permissions.map((p) => p.name) };
  },
});

export const get = query({
  args: {
    id: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.table("users").getX(args.id);
    let imageUrl = user?.imageUrl;
    if (user?.imageId) {
      const image = await ctx.storage.getUrl(user.imageId);
      if (image)
        imageUrl = image;
    }
    return { ...user, imageUrl };
  },
});

export const list = query({
  args: {
    roleId: v.optional(v.id("roles")),
  },
  handler: async (ctx, args) => {
    let query = ctx.table("users");
    if (args.roleId !== undefined) {
      query = query.filter(q => q.eq(q.field("roleId"), args.roleId));
    }

    const users = await query;
    return await Promise.all(users.map(async (user) => {
      let imageUrl = user.imageUrl;
      if (user.imageId && !imageUrl) {
        const image = await ctx.storage.getUrl(user.imageId);
        if (image)
          imageUrl = image;
      }
      return { ...user, imageUrl, role: (await user.edge("role")).name };
    }));
  },
});

export const metadata = query({
  handler: async (ctx) => {
    const users = await ctx.table("users");
    const userRoles = await Promise.all(users.map(async (user) => (await user.edge("role")).name));

    return {
      count: users.length,
      admins: userRoles.filter((role) => role === ROLES.ADMIN).length,
      editors: userRoles.filter((role) => role === ROLES.EDITOR).length,
      viewers: userRoles.filter((role) => role === ROLES.VIEWER).length,
      anonymous: userRoles.filter((role) => role === ROLES.ANONYMOUS).length,
    };
  }
})

export const update = mutation({
  args: {
    id: v.id("users"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    imageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.users.update);
    const { id, ...data } = args;
    const user = await ctx.table("users").getX(id);

    let fullName = user.fullName;
    if (data.firstName || data.lastName) {
      const firstName = data.firstName ?? user.firstName;
      const lastName = data.lastName ?? user.lastName;
      fullName = `${firstName} ${lastName}`;
    }

    return await user.patch({ ...data, fullName });
  },
});

export const remove = mutation({
  args: {
    id: v.id("users"),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.users.delete);
    return await ctx.table("users").getX(args.id).delete();
  },
});

export const changeRole = mutation({
  args: {
    userId: v.id("users"),
    roleId: v.optional(v.id("roles")),
    roleName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await viewerHasPermissionX(ctx, PERMISSIONS.users.update);

    if (args.roleId)
      return await ctx.table("users").getX(args.userId).patch({
        roleId: args.roleId,
      });

    if (args.roleName) {
      const role = await getRole(ctx, args.roleName);
      return await ctx.table("users").getX(args.userId).patch({
        roleId: role._id,
      });
    }
  },
});

export const upsertFromClerk = internalMutation({
  args: { data: v.any() as Validator<UserJSON> }, // no runtime validation, trust Clerk
  async handler(ctx: MutationCtx, { data }) {
    const email = data.email_addresses[0].email_address;
    let role = await getRole(ctx, "Anonymous");

    if (email === ADMIN_EMAIL) {
      role = await getRole(ctx, "Admin");
    }

    const userFields = {
      fullName: `${data.first_name} ${data.last_name}`,
      email,
      imageUrl: data.image_url,
      firstName: data.first_name ?? "",
      lastName: data.last_name ?? "",
      externalId: data.id,
      roleId: role._id,
    };

    const user = await ctx.table("users").get("externalId", data.id);
    if (user === null) {
      await ctx.table("users").insert(userFields);
    }
    else {
      await user.patch({ ...userFields });
    }
  },
});

export const deleteFromClerk = internalMutation({
  args: { clerkUserId: v.string() },
  async handler(ctx: MutationCtx, { clerkUserId }) {
    const user = await ctx.table("users").get("externalId", clerkUserId);

    if (user !== null) {
      await user.delete();
    }
    else {
      console.warn(
        `Can't delete user, there is none for Clerk user ID: ${clerkUserId}`,
      );
    }
  },
});

export async function getCurrentUserOrThrow(ctx: QueryCtx) {
  const userRecord = await getCurrentUser(ctx);
  if (!userRecord)
    throw new Error("Can't get current user");
  return userRecord;
}

export async function getCurrentUser(ctx: QueryCtx) {
  const identity = await ctx.auth.getUserIdentity();
  if (identity === null) {
    return null;
  }
  return await userByExternalId(ctx, identity.subject);
}

async function userByExternalId(ctx: QueryCtx, externalId: string) {
  const user = await ctx.table("users").getX("externalId", externalId);
  const role = await ctx.table("roles").getX(user?.roleId)
  let imageUrl = user?.imageUrl;
  if (user?.imageId) {
    const image = await ctx.storage.getUrl(user.imageId);
    if (image)
      imageUrl = image;
  }
  return { ...user, role, imageUrl };
}

import { TG_BASE_URL } from "@/config/constants";
import { env } from "@/config/env.mjs";
import { tryCatch } from "@/utils/try-catch";

class TelegramApiClient {
  private baseURL: string;

  constructor() {
    this.baseURL = `${TG_BASE_URL}${env.TG_BOT_TOKEN}`;
  }

  public async sendMessage(chat_id: number, text: string, parse_mode?: string) {
    const result = await tryCatch(fetch(`${this.baseURL}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id,
        text,
        parse_mode,
      }),
    }));

    if (!result.success) {
      console.error(`Error sending message to chat id: ${chat_id}`, result.error);
      throw result.error;
    }

    return await result.data.json();
  }

  public async sendToAdmin(text: string, parse_mode?: string) {
    const result = await tryCatch(this.sendMessage(Number(env.TG_ADMIN_CHAT_ID), text, parse_mode));
    if (!result.success) {
      console.error("Error sending message to admin:", result.error);
    }
  }

  public async sendToChats(text: string, parse_mode?: string) {
    const chatIds = env.TG_CHAT_IDS.split(",");

    await Promise.all(chatIds.map(async (chatId) => {
      const result = await tryCatch(this.sendMessage(Number(chatId), text, parse_mode));
      if (!result.success) {
        console.error("Error sending message to chats:", result.error);
      }
    }));
  }
}

export const telegramClient = new TelegramApiClient();
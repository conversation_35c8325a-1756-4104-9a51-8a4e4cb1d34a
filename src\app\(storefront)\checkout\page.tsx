"use client";

import type * as z from "zod";

import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { createOrder } from "@/actions/checkout.actions";
import { Breadcrumbs } from "@/components/dashboard/breadcrumbs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";
import { internalLinks } from "@/config/site-config";
import { useCart } from "@/hooks/use-cart";
import { checkoutFormSchema } from "@/lib/schema";

import OrderItems from "./_components/order-items";

export default function CheckoutPage() {
  const { cartItems, clearCart } = useCart();

  const form = useForm<z.infer<typeof checkoutFormSchema>>({
    resolver: zodResolver(checkoutFormSchema),

    defaultValues: {}
  });

  const onSubmit = async (data: z.infer<typeof checkoutFormSchema>) => {
    const result = await createOrder(data);

    if (result.success) {
      toast.success(result.message);
      clearCart();
    } else {
      toast.error(result.message);
    }
  };

  return (
    <div className="py-8">
      <Breadcrumbs
        breadcrumbs={[
          {
            title: "Home",
            href: "/"
          },
          {
            title: "Checkout",
            href: "/checkout",
            current: true
          }
        ]}
      />

      <>
        <Typography variant="h1" className="my-8">
          Checkout
        </Typography>

        {cartItems.length === 0 ? (
          <div className="py-12 text-center">
            <Typography variant="h2" className="mb-4">
              Your cart is empty
            </Typography>
            <Typography variant="p" className="mb-6 text-gray-500">
              Add items to your cart before proceeding to checkout.
            </Typography>
            <Button asChild>
              <Link href={internalLinks.shop}>Continue Shopping</Link>
            </Button>
          </div>
        ) : (
          <div className="gap-8 grid grid-cols-1 lg:grid-cols-12">
            <Accordion type="single" collapsible className="lg:hidden">
              <AccordionItem value="order-items">
                <AccordionTrigger className="bg-card mb-4 px-4">
                  <Typography variant="h2">Order Items</Typography>
                </AccordionTrigger>
                <AccordionContent>
                  <OrderItems />
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-8 lg:col-span-7"
              >
                <Card className="bg-background">
                  <CardHeader>
                    <Typography variant="h2">Billing Details</Typography>
                  </CardHeader>
                  <CardContent>
                    <div className="gap-6 grid md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input type="email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input type="tel" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem className="md:col-span-2">
                            <FormLabel>Address</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Country</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a country" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="US">
                                  United States
                                </SelectItem>
                                <SelectItem value="CA">Canada</SelectItem>
                                <SelectItem value="GB">
                                  United Kingdom
                                </SelectItem>
                                <SelectItem value="AU">Australia</SelectItem>
                                <SelectItem value="DE">Germany</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="postcode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Postcode</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-background">
                  <CardHeader>
                    <Typography variant="h3">Additional Information</Typography>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="orderNote"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Order Notes (optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Notes about your order, e.g. special notes for delivery"
                              className="placeholder:text-muted placeholder:text-sm"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                <Button type="submit" className="mt-4 w-full">
                  PLACE ORDER
                </Button>
              </form>
            </Form>

            <OrderItems className="lg:col-span-5" />
          </div>
        )}
      </>
    </div>
  );
}

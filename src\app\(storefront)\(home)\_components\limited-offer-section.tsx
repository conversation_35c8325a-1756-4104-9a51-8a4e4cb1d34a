import Link from "next/link";
import React from "react";

import { Section } from "@/components/ds";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { internalLinks } from "@/config/site-config";

export default function LimitedOfferSection() {
  return (
    <Section className="relative bg-cover bg-center py-12">
      <div className="absolute inset-0 bg-black bg-opacity-60 rounded-lg"></div>
      <div className="relative flex flex-col gap-4 p-8 md:p-16 rounded-md text-center">
        <Typography
          variant="h3"
          weight="semibold"
          className="text-background uppercase"
        >
          LIMITED TIME OFFER
        </Typography>
        <Typography as="p" variant="h1" className="font-serif text-background">
          50% OFF
        </Typography>
        <Typography variant="p" className="text-background">
          On selected fragrances
        </Typography>

        <Button asChild variant="secondary">
          <Link
            href={internalLinks.sale}
            className="mx-auto mt-4 px-8 py-3 border-2 rounded w-fit transition-colors"
          >
            SHOP NOW
          </Link>
        </Button>
      </div>
    </Section>
  );
}

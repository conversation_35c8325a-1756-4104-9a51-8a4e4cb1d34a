"use client";

import { useUser } from "@clerk/nextjs";
import { useQuery } from "convex/react";
import { createContext, useCallback } from "react";

import type { Doc } from "@/convex/_generated/dataModel";

import { api } from "@/convex/_generated/api";

type User = Doc<"users"> & {
  role: Doc<"roles">;
  permissions: string[];
};

type AuthContextType = {
  user: User | null | undefined;
  isAuthenticated: boolean | undefined;
  hasPermission: (action: any) => boolean;
  loaded: boolean;
};

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user: cUser, isSignedIn, isLoaded } = useUser();
  const user = useQuery(
    api.users.userWithPermissions,
    isLoaded && cUser ? undefined : "skip"
  );

  const hasPermission = useCallback(
    (action: any): boolean => {
      if (!user) return false;
      return user.permissions.includes(action);
    },
    [user]
  );

  const value = {
    user: cUser === null ? null : (user?.user as User | undefined),
    isAuthenticated: !!cUser && !!user?.user && isSignedIn,
    hasPermission,
    loaded: cUser !== undefined && isLoaded
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

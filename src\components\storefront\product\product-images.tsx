"use client";

import { useEffect, useState } from "react";

import type { CarouselApi } from "@/components/ui/carousel";
import type { Image } from "@/types";

import { ProductImageCenterSection } from "./product-image-center-section";
import { ProductImagesSide } from "./product-images-side";

type ProductImagesProps = {
  images: Image[];
};

export function ProductImages({ images }: ProductImagesProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [thumbsApi, setThumbsApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api || !thumbsApi) {
      return;
    }

    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
      thumbsApi.scrollTo(api.selectedScrollSnap());
    });
  }, [api, thumbsApi]);
  return (
    <div className="flex flex-col gap-4 md:col-span-6">
      <ProductImageCenterSection setApi={setApi} images={images} />
      {images.length > 1 && (
        <ProductImagesSide
          setThumbsApi={setThumbsApi}
          current={current}
          api={api}
          images={images}
        />
      )}
    </div>
  );
}

"use client";

import { Edit, Eye, MoreHorizontal, Trash2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { PERMISSIONS } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";

import type { Brand } from "./columns";

import { DeleteBrandDialog } from "./delete-brand-dialog";

type BrandActionsProps = {
  brand: Brand;
  onDelete: (id: string) => Promise<void>;
};

export function BrandActions({ brand, onDelete }: BrandActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { hasPermission } = useAuth();

  const canEdit = hasPermission(PERMISSIONS.brands.update);
  const canDelete = hasPermission(PERMISSIONS.brands.delete);

  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem asChild>
            <Link href={`/dashboard/brands/${brand._id}`}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </Link>
          </DropdownMenuItem>

          {canEdit && (
            <DropdownMenuItem asChild>
              <Link href={`/dashboard/brands/${brand._id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {canDelete && (
            <DropdownMenuItem
              className="text-red-600"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteBrandDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        brandName={brand.name}
        onConfirm={() => onDelete(brand._id)}
      />
    </div>
  );
}

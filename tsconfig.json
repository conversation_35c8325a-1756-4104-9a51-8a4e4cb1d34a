{"compilerOptions": {"incremental": true, "target": "ES2017", "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "bundler", "paths": {"@/*": ["./src/*"], "@/convex/*": ["./convex/*"]}, "resolveJsonModule": true, "allowJs": true, "strict": true, "noEmit": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
import type { Product } from "@/types";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

import { ProductCard } from "./product-card";

type ProductsCarouselProps = {
  products: Product[];
};

export function ProductsCarousel({ products }: ProductsCarouselProps) {
  if (products.length < 1) return null;

  return (
    <Carousel opts={{ skipSnaps: true }} className="group/content relative">
      <CarouselContent>
        {products.map((product, idx) => (
          <CarouselItem
            className="mx-1 md:mx-2 my-4 basis-1/2 md:basis-1/3 lg:basis-1/4"
            key={`c_${product._id}${idx}`}
          >
            <ProductCard product={product} />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious
        variant="default"
        className="top-1/2 left-0 md:-left-18 md:group-hover/content:left-2 xl:group-hover/content:left-4 absolute xl:flex hover:bg-foreground md:group-hover/content:opacity-100 md:opacity-0 size-10 transition-all -translate-y-1/2 duration-500"
      />
      <CarouselNext
        variant="default"
        className="top-1/2 right-0 md:-right-18 md:group-hover/content:right-2 xl:group-hover/content:right-4 absolute xl:flex hover:bg-foreground md:group-hover/content:opacity-100 md:opacity-0 size-10 transition-all -translate-y-1/2 duration-500"
      />
    </Carousel>
  );
}

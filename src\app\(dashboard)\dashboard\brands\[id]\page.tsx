"use client";

import { useMutation, useQuery } from "convex/react";
import { ArrowLeft, Calendar, Edit, Trash2 } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import type { Id } from "@/convex/_generated/dataModel";

import { RoleGate } from "@/components/dashboard/auth/role-gate";
import { PageSubHeader } from "@/components/dashboard/layout/page-subheader";
import Loading from "@/components/dashboard/loading";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { internalLinks } from "@/config/site-config";
import { api } from "@/convex/_generated/api";
import { ROLES } from "@/convex/permissions";
import { useAuth } from "@/hooks/use-auth";
import { formatDate } from "@/utils";
import { tryCatch } from "@/utils/try-catch";

import { DeleteBrandDialog } from "../_components/delete-brand-dialog";

export default function BrandViewPage() {
  const { id: brandId } = useParams();
  const brand = useQuery(api.brands.get, { id: brandId as Id<"brands"> });
  const deleteBrand = useMutation(api.brands.remove);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { user, loaded } = useAuth();
  const router = useRouter();

  if (loaded && user === null) {
    router.push(internalLinks.home);
    return;
  }
  if (!brand) {
    return <Loading />;
  }

  const handleDeleteBrand = async () => {
    const { success, error } = await tryCatch(deleteBrand({ id: brand._id }));

    if (success) {
      toast.success("Brand deleted");
      router.push("/dashboard/brands");
    } else {
      toast.error("Failed to delete brand", {
        description: error.message
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center justify-between">
        <PageSubHeader
          title={brand.name}
          description={brand.description}
          breadcrumbs={[
            { title: "Dashboard", href: "/dashboard" },
            { title: "Brands", href: "/dashboard/brands" },
            { title: brand.name, current: true }
          ]}
        />

        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/brands">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Brands
            </Link>
          </Button>

          <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
            <Button asChild>
              <Link href={`/dashboard/brands/${brand._id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Brand
              </Link>
            </Button>
          </RoleGate>

          <RoleGate allowedRoles={[ROLES.ADMIN, ROLES.EDITOR]} fallback={null}>
            <Button
              variant="destructive"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </RoleGate>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Brand Information</CardTitle>
            <CardDescription>
              Detailed information about this brand
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-medium">
                  Brand Name:&nbsp;{brand.name}
                </h3>
                <p className="text-muted-foreground">
                  Description:&nbsp;{brand.description}
                </p>
              </div>
              <Badge
                variant={brand.isActive ? "outline" : "secondary"}
                className={
                  brand.isActive
                    ? "bg-green-50 text-green-700"
                    : "bg-amber-50 text-amber-700"
                }
              >
                {brand.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>

            <Separator />

            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Created:</span>
                <span className="ml-2">
                  {formatDate(new Date(brand._creationTime))}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Brand Media</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {brand.logoUrl ? (
              <div>
                <h4 className="mb-2 text-sm font-medium">Logo</h4>
                <div className="w-fit flex items-center justify-center rounded-sm border p-4">
                  <img
                    src={brand.logoUrl}
                    alt={`${brand.name} logo`}
                    className="h-64 w-64 object-contain"
                  />
                </div>
              </div>
            ) : (
              <p>No media</p>
            )}
          </CardContent>
        </Card>
      </div>

      <DeleteBrandDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        brandName={brand.name}
        onConfirm={handleDeleteBrand}
      />
    </div>
  );
}

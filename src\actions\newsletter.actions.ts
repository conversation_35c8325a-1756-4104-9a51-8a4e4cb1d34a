"use server";
import type { z } from "zod";

import { telegramClient } from "@/lib/bot/telegram-client";
import { newsletterFormSchema } from "@/lib/schema";
import { newsletterMessage } from "@/utils/format/newsletter";
import { tryCatch } from "@/utils/try-catch";

export async function subscribeNewsletter(data: z.infer<typeof newsletterFormSchema>) {
  const validateData = newsletterFormSchema.safeParse(data);

  if (!validateData.success) {
    return { success: false, message: "Invalid data" };
  }

  const text = newsletterMessage(data.email);


  const result = await tryCatch(telegramClient.sendToAdmin(text, "Markdown"))

  if (result.error) {
    return { success: false, message: result.error.message ?? "An error occurred. Please try again." };
  }

  return {
    success: true,
    message: "Thank you for subscribing!",
  };
}



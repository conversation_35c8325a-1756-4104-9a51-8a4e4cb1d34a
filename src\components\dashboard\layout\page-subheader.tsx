import type { ReactNode } from "react";

import { cn } from "@/utils";

import { Breadcrumbs } from "../breadcrumbs";

type Breadcrumb = {
  title: string;
  href?: string;
  current?: boolean;
};

type PageHeaderProps = {
  title: string;
  description?: string;
  breadcrumbs?: Breadcrumb[];
  children?: ReactNode;
  className?: string;
};

export function PageSubHeader({
  title,
  description,
  breadcrumbs,
  children,
  className
}: PageHeaderProps) {
  return (
    <div
      className={cn(
        "flex flex-col space-y-2 md:flex-row md:items-start md:justify-between md:space-y-0",
        className
      )}
    >
      <div className="space-y-1">
        {breadcrumbs?.length && <Breadcrumbs breadcrumbs={breadcrumbs} />}
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {description && <p className="text-muted-foreground">{description}</p>}
      </div>
      {children && (
        <div className="flex items-center space-x-2">{children}</div>
      )}
    </div>
  );
}
